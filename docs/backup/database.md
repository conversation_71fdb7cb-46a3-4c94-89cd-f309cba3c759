# EdgeAPI 数据库表结构文档

本文档描述了EdgeAPI系统中所有数据表的结构信息。

生成时间: 2025-07-08
共包含 178 个数据表

## ACMEAuthentication

**文件:** `acme_authentication_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| TaskId | uint64 | taskId |  | Task | 任务ID |
| Domain | string | domain |  |  | 域名 |
| Token | string | token |  |  | 令牌 |
| Key | string | key |  |  | 密钥 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## ACMEProviderAccount

**文件:** `acme_provider_account_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| ProviderCode | string | providerCode |  |  | 代号 |
| EabKid | string | eabKid |  | EabK | KID |
| EabKey | string | eabKey |  |  | Key |
| Error | string | error |  |  | 最后一条错误信息 |
| State | uint8 | state |  |  | 状态 |

## ACMETask

**文件:** `acme_task_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| AcmeUserId | uint32 | acmeUserId |  | AcmeUser | ACME用户ID |
| DnsDomain | string | dnsDomain |  |  | DNS主域名 |
| DnsProviderId | uint64 | dnsProviderId |  | DnsProvider | DNS服务商 |
| Domains | dbs.JSON | domains |  |  | 证书域名 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |
| CertId | uint64 | certId |  | Cert | 生成的证书ID |
| AutoRenew | uint8 | autoRenew |  |  | 是否自动更新 |
| AuthType | string | authType |  |  | 认证类型 |
| AuthURL | string | authURL |  |  | 认证URL |
| Async | bool | async |  |  | 是否异步 |
| Status | uint32 | status |  |  | 任务状态 |

## ACMETaskLog

**文件:** `acme_task_log_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| TaskId | uint64 | taskId |  | Task | 任务ID |
| IsOk | bool | isOk |  |  | 是否成功 |
| Error | string | error |  |  | 错误信息 |
| CreatedAt | uint64 | createdAt |  |  | 运行时间 |

## ACMEUser

**文件:** `acme_user_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| PrivateKey | string | privateKey |  |  | 私钥 |
| Email | string | email |  |  | E-mail |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |
| Description | string | description |  |  | 备注介绍 |
| Registration | dbs.JSON | registration |  |  | 注册信息 |
| ProviderCode | string | providerCode |  |  | 服务商代号 |
| AccountId | uint64 | accountId |  | Account | 提供商ID |

## ADNetwork

**文件:** `ad_network_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |

## ADPackage

**文件:** `ad_package_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| NetworkId | uint32 | networkId |  | Network | 线路ID |
| ProtectionBandwidthSize | uint32 | protectionBandwidthSize |  |  | 防护带宽尺寸 |
| ProtectionBandwidthUnit | string | protectionBandwidthUnit |  |  | 防护带宽单位 |
| ProtectionBandwidthBits | uint64 | protectionBandwidthBits |  |  | 防护带宽比特 |
| ServerBandwidthSize | uint32 | serverBandwidthSize |  |  | 业务带宽尺寸 |
| ServerBandwidthUnit | string | serverBandwidthUnit |  |  | 业务带宽单位 |
| ServerBandwidthBits | uint64 | serverBandwidthBits |  |  | 业务带宽比特 |
| State | uint8 | state |  |  | 状态 |

## ADPackageInstance

**文件:** `ad_package_instance_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| PackageId | uint32 | packageId |  | Package | 规格ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeIds | dbs.JSON | nodeIds |  |  | 节点ID |
| IpAddresses | dbs.JSON | ipAddresses |  |  | IP地址 |
| UserId | uint64 | userId |  | User | 用户ID |
| UserDayTo | string | userDayTo |  |  | 用户有效期YYYYMMDD |
| UserInstanceId | uint64 | userInstanceId |  | UserInstance | 用户实例ID |
| State | uint8 | state |  |  | 状态 |
| ObjectCodes | dbs.JSON | objectCodes |  |  | 防护对象 |

## ADPackagePeriod

**文件:** `ad_package_period_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Count | uint32 | count |  |  | 数量 |
| Unit | string | unit |  |  | 单位：month, year |
| Months | uint32 | months |  |  | 月数 |
| State | uint8 | state |  |  | 状态 |

## ADPackagePrice

**文件:** `ad_package_price_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| PackageId | uint32 | packageId |  | Package | 高防产品ID |
| PeriodId | uint32 | periodId |  | Period | 有效期ID |
| Price | float64 | price |  |  | 价格 |
| DiscountPrice | float64 | discountPrice |  |  | 折后价格 |

## APIAccessToken

**文件:** `api_access_token_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint32 | userId |  | User | 用户ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| Token | string | token |  |  | 令牌 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| ExpiredAt | uint64 | expiredAt |  |  | 过期时间 |

## APIMethodStat

**文件:** `api_method_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ApiNodeId | uint32 | apiNodeId |  | ApiNode | API节点ID |
| Method | string | method |  |  | 方法 |
| Tag | string | tag |  |  | 标签方法 |
| CostMs | float64 | costMs |  |  | 耗时Ms |
| PeekMs | float64 | peekMs |  |  | 峰值耗时 |
| CountCalls | uint64 | countCalls |  |  | 调用次数 |
| Day | string | day |  |  | 日期 |

## APINode

**文件:** `api_node_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| ClusterId | uint32 | clusterId |  | Cluster | 专用集群ID |
| UniqueId | string | uniqueId |  | Unique | 唯一ID |
| Secret | string | secret |  |  | 密钥 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| Http | dbs.JSON | http |  |  | 监听的HTTP配置 |
| Https | dbs.JSON | https |  |  | 监听的HTTPS配置 |
| RestIsOn | uint8 | restIsOn |  |  | 是否开放REST |
| RestHTTP | dbs.JSON | restHTTP |  |  | REST HTTP配置 |
| RestHTTPS | dbs.JSON | restHTTPS |  |  | REST HTTPS配置 |
| AccessAddrs | dbs.JSON | accessAddrs |  |  | 外部访问地址 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| Weight | uint32 | weight |  |  | 权重 |
| Status | dbs.JSON | status |  |  | 运行状态 |
| IsPrimary | bool | isPrimary |  |  | 是否为主API节点 |

## Admin

**文件:** `admin_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Username | string | username |  |  | 用户名 |
| Password | string | password |  |  | 密码 |
| Fullname | string | fullname |  |  | 全名 |
| IsSuper | bool | isSuper |  |  | 是否为超级管理员 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| UpdatedAt | uint64 | updatedAt |  |  | 修改时间 |
| State | uint8 | state |  |  | 状态 |
| Modules | dbs.JSON | modules |  |  | 允许的模块 |
| CanLogin | bool | canLogin |  |  | 是否可以登录 |
| Theme | string | theme |  |  | 模板设置 |
| Lang | string | lang |  |  | 语言代号 |

## ApiToken

**文件:** `api_token_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| NodeId | string | nodeId |  | Node | 节点ID |
| Secret | string | secret |  |  | 节点密钥 |
| Role | string | role |  |  | 节点角色 |
| State | uint8 | state |  |  | 状态 |

## AuthorityKey 已废弃

**文件:** `authority_key_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Value | string | value |  |  | Key值 |
| DayFrom | string | dayFrom |  |  | 开始日期 |
| DayTo | string | dayTo |  |  | 结束日期 |
| Hostname | string | hostname |  |  | Hostname |
| MacAddresses | dbs.JSON | macAddresses |  |  | MAC地址 |
| UpdatedAt | uint64 | updatedAt |  |  | 创建/修改时间 |
| Company | string | company |  |  | 公司组织 |
| RequestCode | string | requestCode |  |  | 申请码 |

## AuthorityNode 已废弃

**文件:** `authority_node_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| UniqueId | string | uniqueId |  | Unique | 唯一ID |
| Secret | string | secret |  |  | 密钥 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| Weight | uint32 | weight |  |  | 权重 |
| Status | dbs.JSON | status |  |  | 运行状态 |

## ClientAgent

**文件:** `client_agent_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 名称 |
| Code | string | code |  |  | 代号 |
| Description | string | description |  |  | 介绍 |
| Order | uint32 | order |  |  | 排序 |
| CountIPs | uint32 | countIPs |  |  | IP数量 |

## ClientAgentIP

**文件:** `client_agent_ip_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AgentId | uint32 | agentId |  | Agent | Agent ID |
| IP | string | ip |  |  | IP地址 |
| Ptr | string | ptr |  |  | PTR值 |

## ClientBrowser

**文件:** `client_browser_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Name | string | name |  |  | 浏览器名称 |
| Codes | dbs.JSON | codes |  |  | 代号 |
| CreatedDay | string | createdDay |  |  | 创建日期YYYYMMDD |
| State | uint8 | state |  |  | 状态 |

## ClientSystem

**文件:** `client_system_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Name | string | name |  |  | 系统名称 |
| Codes | dbs.JSON | codes |  |  | 代号 |
| CreatedDay | string | createdDay |  |  | 创建日期YYYYMMDD |
| State | uint8 | state |  |  | 状态 |

## DBNode

**文件:** `db_node_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Role | string | role |  |  | 数据库角色 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| Host | string | host |  |  | 主机 |
| Port | uint32 | port |  |  | 端口 |
| Database | string | database |  |  | 数据库名称 |
| Username | string | username |  |  | 用户名 |
| Password | string | password |  |  | 密码 |
| Charset | string | charset |  |  | 通讯字符集 |
| ConnTimeout | uint32 | connTimeout |  |  | 连接超时时间（秒） |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Weight | uint32 | weight |  |  | 权重 |
| Order | uint32 | order |  |  | 排序 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |

## DNSDomain

**文件:** `dns_domain_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| ProviderId | uint32 | providerId |  | Provider | 服务商ID |
| IsOn | bool | isOn |  |  | 是否可用 |
| Name | string | name |  |  | 域名 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| DataUpdatedAt | uint64 | dataUpdatedAt |  |  | 数据更新时间 |
| DataError | string | dataError |  |  | 数据更新错误 |
| Data | string | data |  |  | 原始数据信息 |
| Records | dbs.JSON | records |  |  | 所有解析记录 |
| Routes | dbs.JSON | routes |  |  | 线路数据 |
| IsUp | bool | isUp |  |  | 是否在线 |
| State | uint8 | state |  |  | 状态 |
| IsDeleted | bool | isDeleted |  |  | 是否已删除 |

## DNSProvider

**文件:** `dns_provider_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 名称 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Type | string | type |  |  | 供应商类型 |
| ApiParams | dbs.JSON | apiParams |  |  | API参数 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |
| DataUpdatedAt | uint64 | dataUpdatedAt |  |  | 数据同步时间 |
| MinTTL | uint32 | minTTL |  |  | 最小TTL |

## DNSTask

**文件:** `dns_task_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| DomainId | uint32 | domainId |  | Domain | 域名ID |
| RecordName | string | recordName |  |  | 记录名 |
| Type | string | type |  |  | 任务类型 |
| UpdatedAt | uint64 | updatedAt |  |  | 更新时间 |
| IsDone | bool | isDone |  |  | 是否已完成 |
| IsOk | bool | isOk |  |  | 是否成功 |
| Error | string | error |  |  | 错误信息 |
| Version | uint64 | version |  |  | 版本 |
| CountFails | uint32 | countFails |  |  | 尝试失败次数 |

## File

**文件:** `file_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| Code | string | code |  |  | 代号 |
| UserId | uint32 | userId |  | User | 用户ID |
| Description | string | description |  |  | 文件描述 |
| Filename | string | filename |  |  | 文件名 |
| Size | uint32 | size |  |  | 文件尺寸 |
| MimeType | string | mimeType |  |  | Mime类型 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Order | uint32 | order |  |  | 排序 |
| Type | string | type |  |  | 类型 |
| State | uint8 | state |  |  | 状态 |
| IsFinished | bool | isFinished |  |  | 是否已完成上传 |
| IsPublic | bool | isPublic |  |  | 是否可以公开访问 |

## FileChunk

**文件:** `file_chunk_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| FileId | uint32 | fileId |  | File | 文件ID |
| Data | []byte | data |  |  | 分块内容 |

## FormalClientBrowser

**文件:** `formal_client_browser_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 浏览器名称 |
| Codes | dbs.JSON | codes |  |  | 代号 |
| DataId | string | dataId |  | Data | 数据ID |
| State | uint8 | state |  |  | 状态 |

## FormalClientSystem

**文件:** `formal_client_system_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 系统名称 |
| Codes | dbs.JSON | codes |  |  | 代号 |
| State | uint8 | state |  |  | 状态 |
| DataId | string | dataId |  | Data | 数据ID |

## HTTPAccessLog

**文件:** `http_access_log_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| Status | uint32 | status |  |  | 状态码 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Content | dbs.JSON | content |  |  | 日志内容 |
| RequestId | string | requestId |  | Request | 请求ID |
| FirewallPolicyId | uint32 | firewallPolicyId |  | FirewallPolicy | WAF策略ID |
| FirewallRuleGroupId | uint32 | firewallRuleGroupId |  | FirewallRuleGroup | WAF分组ID |
| FirewallRuleSetId | uint32 | firewallRuleSetId |  | FirewallRuleSet | WAF集ID |
| FirewallRuleId | uint32 | firewallRuleId |  | FirewallRule | WAF规则ID |
| RemoteAddr | string | remoteAddr |  |  | IP地址 |
| Domain | string | domain |  |  | 域名 |
| RequestBody | []byte | requestBody |  |  | 请求内容 |
| ResponseBody | []byte | responseBody |  |  | 响应内容 |

## HTTPAccessLogPolicy

**文件:** `http_access_log_policy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| TemplateId | uint32 | templateId |  | Template | 模版ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Name | string | name |  |  | 名称 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Type | string | type |  |  | 存储类型 |
| Options | dbs.JSON | options |  |  | 存储选项 |
| Conds | dbs.JSON | conds |  |  | 请求条件 |
| IsPublic | bool | isPublic |  |  | 是否为公用 |
| FirewallOnly | uint8 | firewallOnly |  |  | 是否只记录防火墙相关 |
| Version | uint32 | version |  |  | 版本号 |
| DisableDefaultDB | bool | disableDefaultDB |  |  | 是否停止默认数据库存储 |

## HTTPAuthPolicy

**文件:** `http_auth_policy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Type | string | type |  |  | 类型 |
| Params | dbs.JSON | params |  |  | 参数 |
| State | uint8 | state |  |  | 状态 |

## HTTPBrotliPolicy

**文件:** `http_brotli_policy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Level | uint32 | level |  |  | 压缩级别 |
| MinLength | dbs.JSON | minLength |  |  | 可压缩最小值 |
| MaxLength | dbs.JSON | maxLength |  |  | 可压缩最大值 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Conds | dbs.JSON | conds |  |  | 条件 |

## HTTPCachePolicy

**文件:** `http_cache_policy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| TemplateId | uint32 | templateId |  | Template | 模版ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Capacity | dbs.JSON | capacity |  |  | 容量数据 |
| MaxKeys | uint64 | maxKeys |  |  | 最多Key值 |
| MaxSize | dbs.JSON | maxSize |  |  | 最大缓存内容尺寸 |
| Type | string | type |  |  | 存储类型 |
| Options | dbs.JSON | options |  |  | 存储选项 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |
| Description | string | description |  |  | 描述 |
| Refs | dbs.JSON | refs |  |  | 默认的缓存设置 |
| SyncCompressionCache | uint8 | syncCompressionCache |  |  | 是否同步写入压缩缓存 |
| FetchTimeout | dbs.JSON | fetchTimeout |  |  | 预热超时时间 |

## HTTPCacheTask

**文件:** `http_cache_task_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Type | string | type |  |  | 任务类型：purge|fetch |
| KeyType | string | keyType |  |  | Key类型 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| DoneAt | uint64 | doneAt |  |  | 完成时间 |
| Day | string | day |  |  | 创建日期YYYYMMDD |
| IsDone | bool | isDone |  |  | 是否已完成 |
| IsOk | bool | isOk |  |  | 是否完全成功 |
| IsReady | bool | isReady |  |  | 是否已准备好 |
| Description | string | description |  |  | 描述 |

## HTTPCacheTaskKey

**文件:** `http_cache_task_key_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| TaskId | uint64 | taskId |  | Task | 任务ID |
| Key | string | key |  |  | Key |
| KeyType | string | keyType |  |  | Key类型：key|prefix |
| Type | string | type |  |  | 操作类型 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| Nodes | dbs.JSON | nodes |  |  | 节点 |
| Errors | dbs.JSON | errors |  |  | 错误信息 |
| IsDone | bool | isDone |  |  | 是否已完成 |

## HTTPDeflatePolicy

**文件:** `http_deflate_policy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Level | uint32 | level |  |  | 压缩级别 |
| MinLength | dbs.JSON | minLength |  |  | 可压缩最小值 |
| MaxLength | dbs.JSON | maxLength |  |  | 可压缩最大值 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Conds | dbs.JSON | conds |  |  | 条件 |

## HTTPFastcgi

**文件:** `http_fastcgi_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Address | string | address |  |  | 地址 |
| Params | dbs.JSON | params |  |  | 参数 |
| ReadTimeout | dbs.JSON | readTimeout |  |  | 读取超时 |
| ConnTimeout | dbs.JSON | connTimeout |  |  | 连接超时 |
| PoolSize | uint32 | poolSize |  |  | 连接池尺寸 |
| PathInfoPattern | string | pathInfoPattern |  |  | PATH_INFO匹配 |
| State | uint8 | state |  |  | 状态 |

## HTTPFirewallPolicy

**文件:** `http_firewall_policy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| TemplateId | uint32 | templateId |  | Template | 模版ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| GroupId | uint32 | groupId |  | Group | 服务分组ID |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| Inbound | dbs.JSON | inbound |  |  | 入站规则 |
| Outbound | dbs.JSON | outbound |  |  | 出站规则 |
| BlockOptions | dbs.JSON | blockOptions |  |  | BLOCK动作选项 |
| PageOptions | dbs.JSON | pageOptions |  |  | PAGE动作选项 |
| CaptchaOptions | dbs.JSON | captchaOptions |  |  | 验证码动作选项 |
| JsCookieOptions | dbs.JSON | jsCookieOptions |  |  | JSCookie动作选项 |
| Mode | string | mode |  |  | 模式 |
| UseLocalFirewall | uint8 | useLocalFirewall |  |  | 是否自动使用本地防火墙 |
| SynFlood | dbs.JSON | synFlood |  |  | SynFlood防御设置 |
| Log | dbs.JSON | log |  |  | 日志配置 |
| MaxRequestBodySize | uint32 | maxRequestBodySize |  |  | 可以检查的最大请求内容尺寸 |
| DenyCountryHTML | string | denyCountryHTML |  |  | 区域封禁提示 |
| DenyProvinceHTML | string | denyProvinceHTML |  |  | 省份封禁提示 |

## HTTPFirewallRule

**文件:** `http_firewall_rule_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Description | string | description |  |  | 说明 |
| Param | string | param |  |  | 参数 |
| ParamFilters | dbs.JSON | paramFilters |  |  | 处理器 |
| Operator | string | operator |  |  | 操作符 |
| Value | string | value |  |  | 对比值 |
| IsCaseInsensitive | bool | isCaseInsensitive |  |  | 是否大小写不敏感 |
| CheckpointOptions | dbs.JSON | checkpointOptions |  |  | 检查点参数 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |

## HTTPFirewallRuleGroup

**文件:** `http_firewall_rule_group_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| Code | string | code |  |  | 代号 |
| IsTemplate | bool | isTemplate |  |  | 是否为预置模板 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| State | uint8 | state |  |  | 状态 |
| Sets | dbs.JSON | sets |  |  | 规则集列表 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## HTTPFirewallRuleSet

**文件:** `http_firewall_rule_set_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Code | string | code |  |  | 代号 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Rules | dbs.JSON | rules |  |  | 规则列表 |
| Connector | string | connector |  |  | 规则之间的关系 |
| State | uint8 | state |  |  | 状态 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Action | string | action |  |  | 执行的动作（过期） |
| ActionOptions | dbs.JSON | actionOptions |  |  | 动作的选项（过期） |
| Actions | dbs.JSON | actions |  |  | 一组动作 |
| IgnoreLocal | bool | ignoreLocal |  |  | 忽略局域网请求 |
| IgnoreSearchEngine | bool | ignoreSearchEngine |  |  | 忽略搜索引擎 |

## HTTPGzip

**文件:** `http_gzip_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Level | uint32 | level |  |  | 压缩级别 |
| MinLength | dbs.JSON | minLength |  |  | 可压缩最小值 |
| MaxLength | dbs.JSON | maxLength |  |  | 可压缩最大值 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Conds | dbs.JSON | conds |  |  | 条件 |

## HTTPHeader

**文件:** `http_header_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| TemplateId | uint32 | templateId |  | Template | 模版ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Value | string | value |  |  | 值 |
| Order | uint32 | order |  |  | 排序 |
| Status | dbs.JSON | status |  |  | 状态码设置 |
| DisableRedirect | uint8 | disableRedirect |  |  | 是否不支持跳转 |
| ShouldAppend | bool | shouldAppend |  |  | 是否为附加 |
| ShouldReplace | bool | shouldReplace |  |  | 是否替换变量 |
| ReplaceValues | dbs.JSON | replaceValues |  |  | 替换的值 |
| Methods | dbs.JSON | methods |  |  | 支持的方法 |
| Domains | dbs.JSON | domains |  |  | 支持的域名 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## HTTPHeaderPolicy

**文件:** `http_header_policy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| State | uint8 | state |  |  | 状态 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| AddHeaders | dbs.JSON | addHeaders |  |  | 添加的Header |
| AddTrailers | dbs.JSON | addTrailers |  |  | 添加的Trailers |
| SetHeaders | dbs.JSON | setHeaders |  |  | 设置Header |
| ReplaceHeaders | dbs.JSON | replaceHeaders |  |  | 替换Header内容 |
| Expires | dbs.JSON | expires |  |  | Expires单独设置 |
| DeleteHeaders | dbs.JSON | deleteHeaders |  |  | 删除的Headers |
| NonStandardHeaders | dbs.JSON | nonStandardHeaders |  |  | 非标Headers |
| Cors | dbs.JSON | cors |  |  | CORS配置 |

## HTTPLocation

**文件:** `http_location_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| TemplateId | uint32 | templateId |  | Template | 模版ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| ParentId | uint32 | parentId |  | Parent | 父级ID |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Pattern | string | pattern |  |  | 匹配规则 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| WebId | uint32 | webId |  | Web | Web配置ID |
| ReverseProxy | dbs.JSON | reverseProxy |  |  | 反向代理 |
| UrlPrefix | string | urlPrefix |  |  | URL前缀 |
| IsBreak | bool | isBreak |  |  | 是否终止匹配 |
| Conds | dbs.JSON | conds |  |  | 匹配条件 |
| Domains | dbs.JSON | domains |  |  | 专属域名 |

## HTTPPage

**文件:** `http_page_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| StatusList | dbs.JSON | statusList |  |  | 状态列表 |
| Url | string | url |  |  | 页面URL |
| NewStatus | int32 | newStatus |  |  | 新状态码 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Body | string | body |  |  | 页面内容 |
| BodyType | string | bodyType |  |  | 内容类型 |
| ExceptURLPatterns | dbs.JSON | exceptURLPatterns |  |  | 例外URL |
| OnlyURLPatterns | dbs.JSON | onlyURLPatterns |  |  | 限制URL |

## HTTPRewriteRule

**文件:** `http_rewrite_rule_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| TemplateId | uint32 | templateId |  | Template | 模版ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Pattern | string | pattern |  |  | 匹配规则 |
| Replace | string | replace |  |  | 跳转后的地址 |
| Mode | string | mode |  |  | 替换模式 |
| RedirectStatus | uint32 | redirectStatus |  |  | 跳转的状态码 |
| ProxyHost | string | proxyHost |  |  | 代理的主机名 |
| IsBreak | bool | isBreak |  |  | 是否终止解析 |
| WithQuery | uint8 | withQuery |  |  | 是否保留URI参数 |
| Conds | dbs.JSON | conds |  |  | 匹配条件 |

## HTTPWeb

**文件:** `http_web_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| TemplateId | uint32 | templateId |  | Template | 模版ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Root | dbs.JSON | root |  |  | 根目录 |
| Charset | dbs.JSON | charset |  |  | 字符集 |
| Shutdown | dbs.JSON | shutdown |  |  | 临时关闭页面配置 |
| Pages | dbs.JSON | pages |  |  | 特殊页面 |
| EnableGlobalPages | bool | enableGlobalPages |  |  | 是否启用系统配置的自定义页面 |
| RedirectToHttps | dbs.JSON | redirectToHttps |  |  | 跳转到HTTPS设置 |
| Indexes | dbs.JSON | indexes |  |  | 首页文件列表 |
| MaxRequestBodySize | dbs.JSON | maxRequestBodySize |  |  | 最大允许的请求内容尺寸 |
| RequestHeader | dbs.JSON | requestHeader |  |  | 请求Header配置 |
| ResponseHeader | dbs.JSON | responseHeader |  |  | 响应Header配置 |
| AccessLog | dbs.JSON | accessLog |  |  | 访问日志配置 |
| Stat | dbs.JSON | stat |  |  | 统计配置 |
| Gzip | dbs.JSON | gzip |  |  | Gzip配置（v0.3.2弃用） |
| Compression | dbs.JSON | compression |  |  | 压缩配置 |
| Cache | dbs.JSON | cache |  |  | 缓存配置 |
| Firewall | dbs.JSON | firewall |  |  | 防火墙设置 |
| Locations | dbs.JSON | locations |  |  | 路由规则配置 |
| Websocket | dbs.JSON | websocket |  |  | Websocket设置 |
| RewriteRules | dbs.JSON | rewriteRules |  |  | 重写规则配置 |
| HostRedirects | dbs.JSON | hostRedirects |  |  | 域名跳转 |
| Fastcgi | dbs.JSON | fastcgi |  |  | Fastcgi配置 |
| Auth | dbs.JSON | auth |  |  | 认证策略配置 |
| Webp | dbs.JSON | webp |  |  | WebP配置 |
| RemoteAddr | dbs.JSON | remoteAddr |  |  | 客户端IP配置 |
| MergeSlashes | uint8 | mergeSlashes |  |  | 是否合并路径中的斜杠 |
| RequestLimit | dbs.JSON | requestLimit |  |  | 请求限制 |
| RequestScripts | dbs.JSON | requestScripts |  |  | 请求脚本 |
| Uam | dbs.JSON | uam |  |  | UAM设置 |
| Cc | dbs.JSON | cc |  |  | CC设置 |
| Referers | dbs.JSON | referers |  |  | 防盗链设置 |
| UserAgent | dbs.JSON | userAgent |  |  | UserAgent设置 |
| Optimization | dbs.JSON | optimization |  |  | 页面优化配置 |
| Hls | dbs.JSON | hls |  |  | HLS设置 |

## HTTPWebsocket

**文件:** `http_websocket_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |
| IsOn | bool | isOn |  |  | 是否启用 |
| HandshakeTimeout | dbs.JSON | handshakeTimeout |  |  | 握手超时时间 |
| AllowAllOrigins | uint8 | allowAllOrigins |  |  | 是否支持所有源 |
| AllowedOrigins | dbs.JSON | allowedOrigins |  |  | 支持的源域名列表 |
| RequestSameOrigin | uint8 | requestSameOrigin |  |  | 是否请求一样的Origin |
| RequestOrigin | string | requestOrigin |  |  | 请求Origin |
| WebId | uint64 | webId |  | Web | Web |

## IPItem

**文件:** `ip_item_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ListId | uint32 | listId |  | List | 所属名单ID |
| Value | string | value |  |  | 原始值 |
| Type | string | type |  |  | 类型 |
| IpFrom | string | ipFrom |  |  | 开始IP |
| IpTo | string | ipTo |  |  | 结束IP |
| IpFromLong | uint64 | ipFromLong |  |  | 开始IP整型（弃用） |
| IpToLong | uint64 | ipToLong |  |  | 结束IP整型（弃用） |
| Version | uint64 | version |  |  | 版本 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| UpdatedAt | uint64 | updatedAt |  |  | 修改时间 |
| Reason | string | reason |  |  | 加入说明 |
| EventLevel | string | eventLevel |  |  | 事件级别 |
| State | uint8 | state |  |  | 状态 |
| ExpiredAt | uint64 | expiredAt |  |  | 过期时间 |
| ServerId | uint32 | serverId |  | Server | 有效范围服务ID |
| NodeId | uint32 | nodeId |  | Node | 有效范围节点ID |
| SourceNodeId | uint32 | sourceNodeId |  | SourceNode | 来源节点ID |
| SourceServerId | uint32 | sourceServerId |  | SourceServer | 来源服务ID |
| SourceHTTPFirewallPolicyId | uint32 | sourceHTTPFirewallPolicyId |  | SourceHTTPFirewallPolicy | 来源策略ID |
| SourceHTTPFirewallRuleGroupId | uint32 | sourceHTTPFirewallRuleGroupId |  | SourceHTTPFirewallRuleGroup | 来源规则集分组ID |
| SourceHTTPFirewallRuleSetId | uint32 | sourceHTTPFirewallRuleSetId |  | SourceHTTPFirewallRuleSet | 来源规则集ID |
| SourceUserId | uint64 | sourceUserId |  | SourceUser | 用户ID |
| IsRead | bool | isRead |  |  | 是否已读 |

## IPLibrary

**文件:** `ip_library_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| FileId | uint32 | fileId |  | File | 文件ID |
| Type | string | type |  |  | 类型 |
| Name | string | name |  |  | 名称 |
| IsPublic | bool | isPublic |  |  | 是否公用 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## IPLibraryArtifact

**文件:** `ip_library_artifact_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 名称 |
| FileId | uint64 | fileId |  | File | 文件ID |
| LibraryFileId | uint32 | libraryFileId |  | LibraryFile | IP库文件ID |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Meta | dbs.JSON | meta |  |  | 元数据 |
| IsPublic | bool | isPublic |  |  | 是否为公用 |
| Code | string | code |  |  | 代号 |
| State | uint8 | state |  |  | 状态 |

## IPLibraryFile

**文件:** `ip_library_file_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Name | string | name |  |  | IP库名称 |
| FileId | uint64 | fileId |  | File | 原始文件ID |
| Template | string | template |  |  | 模板 |
| EmptyValues | dbs.JSON | emptyValues |  |  | 空值列表 |
| GeneratedFileId | uint64 | generatedFileId |  | GeneratedFile | 生成的文件ID |
| GeneratedAt | uint64 | generatedAt |  |  | 生成时间 |
| IsFinished | bool | isFinished |  |  | 是否已经完成 |
| Countries | dbs.JSON | countries |  |  | 国家/地区 |
| Provinces | dbs.JSON | provinces |  |  | 省份 |
| Cities | dbs.JSON | cities |  |  | 城市 |
| Towns | dbs.JSON | towns |  |  | 区县 |
| Providers | dbs.JSON | providers |  |  | ISP服务商 |
| Code | string | code |  |  | 文件代号 |
| Password | string | password |  |  | 密码 |
| CreatedAt | uint64 | createdAt |  |  | 上传时间 |
| State | uint8 | state |  |  | 状态 |

## IPList

**文件:** `ip_list_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Type | string | type |  |  | 类型 |
| AdminId | uint32 | adminId |  | Admin | 用户ID |
| UserId | uint32 | userId |  | User | 用户ID |
| ServerId | uint64 | serverId |  | Server | 服务ID |
| Name | string | name |  |  | 列表名 |
| Code | string | code |  |  | 代号 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Timeout | dbs.JSON | timeout |  |  | 默认超时时间 |
| Actions | dbs.JSON | actions |  |  | IP触发的动作 |
| Description | string | description |  |  | 描述 |
| IsPublic | bool | isPublic |  |  | 是否公用 |
| IsGlobal | bool | isGlobal |  |  | 是否全局 |

## LatestItem

**文件:** `latest_item_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ItemType | string | itemType |  |  | Item类型 |
| ItemId | uint64 | itemId |  | Item | itemID |
| Count | uint64 | count |  |  | 数量 |
| UpdatedAt | uint64 | updatedAt |  |  | 更新时间 |

## Log

**文件:** `log_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Level | string | level |  |  | 级别 |
| Description | string | description |  |  | 描述 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Action | string | action |  |  | 动作 |
| UserId | uint32 | userId |  | User | 用户ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| ProviderId | uint32 | providerId |  | Provider | 供应商ID |
| Ip | string | ip |  |  | IP地址 |
| Type | string | type |  |  | 类型：admin, user |
| Day | string | day |  |  | 日期 |
| BillId | uint32 | billId |  | Bill | 账单ID |
| LangMessageCode | string | langMessageCode |  |  | 多语言消息代号 |
| LangMessageArgs | dbs.JSON | langMessageArgs |  |  | 多语言参数 |
| Params | dbs.JSON | params |  |  | 关联对象参数 |

## Login

**文件:** `login_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Type | string | type |  |  | 认证方式 |
| Params | dbs.JSON | params |  |  | 参数 |
| State | uint8 | state |  |  | 状态 |

## LoginSession

**文件:** `login_session_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AdminId | uint64 | adminId |  | Admin | 管理员ID |
| UserId | uint64 | userId |  | User | 用户ID |
| Sid | string | sid |  | S | 令牌 |
| Values | dbs.JSON | values |  |  | 数据 |
| Ip | string | ip |  |  | 登录IP |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| ExpiresAt | uint64 | expiresAt |  |  | 过期时间 |

## LoginTicket

**文件:** `login_ticket_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ExpiresAt | uint64 | expiresAt |  |  | 过期时间 |
| Value | string | value |  |  | 票据值 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Ip | string | ip |  |  | 用户IP |

## Message

**文件:** `message_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Role | string | role |  |  | 角色 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| Level | string | level |  |  | 级别 |
| Subject | string | subject |  |  | 标题 |
| Body | string | body |  |  | 内容 |
| Type | string | type |  |  | 消息类型 |
| Params | dbs.JSON | params |  |  | 额外的参数 |
| IsRead | bool | isRead |  |  | 是否已读 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Day | string | day |  |  | 日期YYYYMMDD |
| Hash | string | hash |  |  | 消息内容的Hash |

## MessageMedia

**文件:** `message_media_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 名称 |
| Type | string | type |  |  | 类型 |
| Description | string | description |  |  | 描述 |
| UserDescription | string | userDescription |  |  | 用户描述 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |

## MessageMediaInstance

**文件:** `message_media_instance_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 名称 |
| IsOn | bool | isOn |  |  | 是否启用 |
| MediaType | string | mediaType |  |  | 媒介类型 |
| Params | dbs.JSON | params |  |  | 媒介参数 |
| Description | string | description |  |  | 备注 |
| Rate | dbs.JSON | rate |  |  | 发送频率 |
| State | uint8 | state |  |  | 状态 |
| HashLife | int32 | hashLife |  |  | HASH有效期（秒） |

## MessageReceiver

**文件:** `message_receiver_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Role | string | role |  |  | 节点角色 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| Type | string | type |  |  | 类型 |
| Params | dbs.JSON | params |  |  | 参数 |
| RecipientId | uint32 | recipientId |  | Recipient | 接收人ID |
| RecipientGroupId | uint32 | recipientGroupId |  | RecipientGroup | 接收人分组ID |
| State | uint8 | state |  |  | 状态 |

## MessageRecipient

**文件:** `message_recipient_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| InstanceId | uint32 | instanceId |  | Instance | 实例ID |
| User | string | user |  |  | 接收人信息 |
| GroupIds | dbs.JSON | groupIds |  |  | 分组ID |
| State | uint8 | state |  |  | 状态 |
| TimeFrom | string | timeFrom |  |  | 开始时间 |
| TimeTo | string | timeTo |  |  | 结束时间 |
| Description | string | description |  |  | 备注 |

## MessageRecipientGroup

**文件:** `message_recipient_group_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 分组名 |
| Order | uint32 | order |  |  | 排序 |
| IsOn | bool | isOn |  |  | 是否启用 |
| State | uint8 | state |  |  | 状态 |

## MessageTask

**文件:** `message_task_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| RecipientId | uint32 | recipientId |  | Recipient | 接收人ID |
| Hash | string | hash |  |  | SUM标识 |
| InstanceId | uint32 | instanceId |  | Instance | 媒介实例ID |
| User | string | user |  |  | 接收用户标识 |
| Subject | string | subject |  |  | 标题 |
| Body | string | body |  |  | 内容 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Status | uint8 | status |  |  | 发送状态 |
| SentAt | uint64 | sentAt |  |  | 最后一次发送时间 |
| State | uint8 | state |  |  | 状态 |
| Result | dbs.JSON | result |  |  | 结果 |
| Day | string | day |  |  | YYYYMMDD |
| IsPrimary | bool | isPrimary |  |  | 是否优先 |

## MessageTaskLog

**文件:** `message_task_log_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| TaskId | uint64 | taskId |  | Task | 任务ID |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| IsOk | bool | isOk |  |  | 是否成功 |
| Error | string | error |  |  | 错误信息 |
| Response | string | response |  |  | 响应信息 |
| Day | string | day |  |  | YYYYMMDD |

## MetricChart

**文件:** `metric_chart_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| ItemId | uint32 | itemId |  | Item | 指标ID |
| Name | string | name |  |  | 名称 |
| Code | string | code |  |  | 代号 |
| Type | string | type |  |  | 图形类型 |
| WidthDiv | int32 | widthDiv |  |  | 宽度划分 |
| Params | dbs.JSON | params |  |  | 图形参数 |
| Order | uint32 | order |  |  | 排序 |
| IsOn | bool | isOn |  |  | 是否启用 |
| State | uint8 | state |  |  | 状态 |
| MaxItems | uint32 | maxItems |  |  | 最多条目 |
| IgnoreEmptyKeys | uint8 | ignoreEmptyKeys |  |  | 忽略空的键值 |
| IgnoredKeys | dbs.JSON | ignoredKeys |  |  | 忽略键值 |

## MetricItem

**文件:** `metric_item_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Code | string | code |  |  | 代号（用来区分是否内置） |
| Category | string | category |  |  | 类型，比如http, tcp等 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Name | string | name |  |  | 指标名称 |
| Keys | dbs.JSON | keys |  |  | 统计的Key |
| Period | uint32 | period |  |  | 周期 |
| PeriodUnit | string | periodUnit |  |  | 周期单位 |
| ExpiresPeriod | uint32 | expiresPeriod |  |  | 过期周期 |
| Value | string | value |  |  | 值运算 |
| State | uint8 | state |  |  | 状态 |
| Version | uint32 | version |  |  | 版本号 |
| IsPublic | bool | isPublic |  |  | 是否为公用 |
| LastTime | string | lastTime |  |  | 最新时间 |

## MetricStat

**文件:** `metric_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Hash | string | hash |  |  | Hash值 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| ItemId | uint64 | itemId |  | Item | 指标 |
| Keys | dbs.JSON | keys |  |  | 键值 |
| Value | float64 | value |  |  | 数值 |
| Time | string | time |  |  | 分钟值YYYYMMDDHHII |
| Version | uint32 | version |  |  | 版本号 |
| CreatedDay | string | createdDay |  |  | YYYYMMDD |

## MetricSumStat

**文件:** `metric_sum_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| ItemId | uint64 | itemId |  | Item | 指标 |
| Count | uint64 | count |  |  | 数量 |
| Total | float64 | total |  |  | 总和 |
| Time | string | time |  |  | 分钟值YYYYMMDDHHII |
| Version | uint32 | version |  |  | 版本号 |
| CreatedDay | string | createdDay |  |  | 创建日期YYYYMMDD |

## NSAccessLog

**文件:** `ns_access_log_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| DomainId | uint32 | domainId |  | Domain | 域名ID |
| RecordId | uint32 | recordId |  | Record | 记录ID |
| Content | dbs.JSON | content |  |  | 访问数据 |
| RequestId | string | requestId |  | Request | 请求ID |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| RemoteAddr | string | remoteAddr |  |  | IP |

## NSCluster

**文件:** `ns_cluster_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 集群名 |
| InstallDir | string | installDir |  |  | 安装目录 |
| State | uint8 | state |  |  | 状态 |
| AccessLog | dbs.JSON | accessLog |  |  | 访问日志配置 |
| GrantId | uint32 | grantId |  | Grant | 授权ID |
| Recursion | dbs.JSON | recursion |  |  | 递归DNS设置 |
| Tcp | dbs.JSON | tcp |  |  | TCP设置 |
| Tls | dbs.JSON | tls |  |  | TLS设置 |
| Udp | dbs.JSON | udp |  |  | UDP设置 |
| Doh | dbs.JSON | doh |  |  | DoH设置 |
| DdosProtection | dbs.JSON | ddosProtection |  |  | DDoS防护设置 |
| Hosts | dbs.JSON | hosts |  |  | DNS主机地址 |
| Soa | dbs.JSON | soa |  |  | SOA配置 |
| AutoRemoteStart | bool | autoRemoteStart |  |  | 自动远程启动 |
| TimeZone | string | timeZone |  |  | 时区 |
| Answer | dbs.JSON | answer |  |  | 应答设置 |
| SoaSerial | uint64 | soaSerial |  |  | SOA序列号 |
| Email | string | email |  |  | 管理员邮箱 |
| DetectAgents | bool | detectAgents |  |  | 是否监测Agents |
| CheckingPorts | bool | checkingPorts |  |  | 自动检测端口 |

## NSDomain

**文件:** `ns_domain_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 域名 |
| GroupIds | dbs.JSON | groupIds |  |  | 分组ID |
| Tsig | dbs.JSON | tsig |  |  | TSIG配置 |
| VerifyTXT | string | verifyTXT |  |  | 验证用的TXT |
| VerifyExpiresAt | uint64 | verifyExpiresAt |  |  | 验证TXT过期时间 |
| RecordsHealthCheck | dbs.JSON | recordsHealthCheck |  |  | 记录健康检查设置 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Version | uint64 | version |  |  | 版本号 |
| Status | string | status |  |  | 状态：none|verified |
| State | uint8 | state |  |  | 状态 |

## NSDomainGroup

**文件:** `ns_domain_group_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| Name | string | name |  |  | 分组名称 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |

## NSKey

**文件:** `ns_key_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 状态 |
| Name | string | name |  |  | 名称 |
| DomainId | uint64 | domainId |  | Domain | 域名ID |
| ZoneId | uint64 | zoneId |  | Zone | 子域ID |
| Algo | string | algo |  |  | 算法 |
| Secret | string | secret |  |  | 密码 |
| SecretType | string | secretType |  |  | 密码类型 |
| Version | uint64 | version |  |  | 版本号 |
| State | uint8 | state |  |  | 状态 |

## NSNode

**文件:** `ns_node_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| Name | string | name |  |  | 节点名称 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Status | dbs.JSON | status |  |  | 运行状态 |
| UniqueId | string | uniqueId |  | Unique | 节点ID |
| Secret | string | secret |  |  | 密钥 |
| IsUp | bool | isUp |  |  | 是否运行 |
| IsInstalled | bool | isInstalled |  |  | 是否已安装 |
| InstallStatus | dbs.JSON | installStatus |  |  | 安装状态 |
| InstallDir | string | installDir |  |  | 安装目录 |
| State | uint8 | state |  |  | 状态 |
| IsActive | bool | isActive |  |  | 是否活跃 |
| StatusIsNotified | uint8 | statusIsNotified |  |  | 活跃状态已经通知 |
| InactiveNotifiedAt | uint64 | inactiveNotifiedAt |  |  | 离线通知时间 |
| ConnectedAPINodes | dbs.JSON | connectedAPINodes |  |  | 当前连接的API节点 |
| DdosProtection | dbs.JSON | ddosProtection |  |  | DDoS防护设置 |
| ApiNodeAddrs | dbs.JSON | apiNodeAddrs |  |  | API节点地址 |

## NSPlan

**文件:** `ns_plan_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 套餐名称 |
| IsOn | bool | isOn |  |  | 是否启用 |
| MonthlyPrice | float64 | monthlyPrice |  |  | 月价格 |
| YearlyPrice | float64 | yearlyPrice |  |  | 年价格 |
| Order | uint32 | order |  |  | 排序 |
| Config | dbs.JSON | config |  |  | 配置 |
| State | uint8 | state |  |  | 状态 |

## NSQuestionOption

**文件:** `ns_question_option_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Name | string | name |  |  | 选项名 |
| Values | dbs.JSON | values |  |  | 选项值 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## NSRecord

**文件:** `ns_record_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| DomainId | uint32 | domainId |  | Domain | 域名ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Description | string | description |  |  | 备注 |
| Name | string | name |  |  | 记录名 |
| Type | string | type |  |  | 类型 |
| Value | string | value |  |  | 值 |
| MxPriority | uint32 | mxPriority |  |  | MX优先级 |
| SrvPriority | uint32 | srvPriority |  |  | SRV优先级 |
| SrvWeight | uint32 | srvWeight |  |  | SRV权重 |
| SrvPort | uint32 | srvPort |  |  | SRV端口 |
| CaaFlag | uint8 | caaFlag |  |  | CAA Flag |
| CaaTag | string | caaTag |  |  | CAA TAG |
| Ttl | uint32 | ttl |  |  | TTL（秒） |
| Weight | uint32 | weight |  |  | 权重 |
| RouteIds | dbs.JSON | routeIds |  |  | 线路 |
| HealthCheck | dbs.JSON | healthCheck |  |  | 健康检查配置 |
| CountUp | uint32 | countUp |  |  | 连续上线次数 |
| CountDown | uint32 | countDown |  |  | 连续离线次数 |
| IsUp | bool | isUp |  |  | 是否在线 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Version | uint64 | version |  |  | 版本号 |
| State | uint8 | state |  |  | 状态 |

## NSRecordHourlyStat

**文件:** `ns_record_hourly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| DomainId | uint64 | domainId |  | Domain | 域名ID |
| RecordId | uint64 | recordId |  | Record | 记录ID |
| Day | string | day |  |  | YYYYMMDD |
| Hour | string | hour |  |  | YYYYMMDDHH |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| Bytes | uint64 | bytes |  |  | 流量 |

## NSRoute

**文件:** `ns_route_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| CategoryId | uint32 | categoryId |  | Category | 分类ID |
| DomainId | uint64 | domainId |  | Domain | 域名ID |
| AdminId | uint64 | adminId |  | Admin | 管理员ID |
| UserId | uint64 | userId |  | User | 用户ID |
| IsPublic | bool | isPublic |  |  | 是否公用（管理员创建的线路） |
| Name | string | name |  |  | 名称 |
| Ranges | dbs.JSON | ranges |  |  | 范围 |
| Order | uint32 | order |  |  | 排序 |
| Version | uint64 | version |  |  | 版本号 |
| Priority | uint32 | priority |  |  | 优先级，越高越优先 |
| Code | string | code |  |  | 代号 |
| State | uint8 | state |  |  | 状态 |

## NSRouteCategory

**文件:** `ns_route_category_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 分类名 |
| AdminId | uint64 | adminId |  | Admin | 管理员ID |
| UserId | uint64 | userId |  | User | 用户ID |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |

## NSUserPlan

**文件:** `ns_user_plan_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| PlanId | uint32 | planId |  | Plan | 套餐ID |
| DayFrom | string | dayFrom |  |  | YYYYMMDD |
| DayTo | string | dayTo |  |  | YYYYMMDD |
| PeriodUnit | string | periodUnit |  |  | monthly|yearly |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |

## NSZone

**文件:** `ns_zone_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| DomainId | uint64 | domainId |  | Domain | 域名ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Order | uint32 | order |  |  | 排序 |
| Version | uint64 | version |  |  | 版本 |
| Tsig | dbs.JSON | tsig |  |  | TSIG配置 |
| State | uint8 | state |  |  | 状态 |

## Node

**文件:** `node_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Level | uint8 | level |  |  | 级别 |
| LnAddrs | dbs.JSON | lnAddrs |  |  | Ln级别访问地址 |
| IsOn | bool | isOn |  |  | 是否启用 |
| IsUp | bool | isUp |  |  | 是否在线 |
| CountUp | uint32 | countUp |  |  | 连续在线次数 |
| CountDown | uint32 | countDown |  |  | 连续下线次数 |
| IsActive | bool | isActive |  |  | 是否活跃 |
| InactiveNotifiedAt | uint64 | inactiveNotifiedAt |  |  | 离线通知时间 |
| UniqueId | string | uniqueId |  | Unique | 节点ID |
| Secret | string | secret |  |  | 密钥 |
| Name | string | name |  |  | 节点名 |
| Code | string | code |  |  | 代号 |
| ClusterId | uint32 | clusterId |  | Cluster | 主集群ID |
| SecondaryClusterIds | dbs.JSON | secondaryClusterIds |  |  | 从集群ID |
| RegionId | uint32 | regionId |  | Region | 区域ID |
| GroupId | uint32 | groupId |  | Group | 分组ID |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Status | dbs.JSON | status |  |  | 最新的状态 |
| Version | uint32 | version |  |  | 当前版本号 |
| LatestVersion | uint32 | latestVersion |  |  | 最后版本号 |
| InstallDir | string | installDir |  |  | 安装目录 |
| IsInstalled | bool | isInstalled |  |  | 是否已安装 |
| InstallStatus | dbs.JSON | installStatus |  |  | 安装状态 |
| State | uint8 | state |  |  | 状态 |
| ConnectedAPINodes | dbs.JSON | connectedAPINodes |  |  | 当前连接的API节点 |
| MaxCPU | uint32 | maxCPU |  |  | 可以使用的最多CPU |
| MaxThreads | uint32 | maxThreads |  |  | 最大线程数 |
| DdosProtection | dbs.JSON | ddosProtection |  |  | DDOS配置 |
| DnsRoutes | dbs.JSON | dnsRoutes |  |  | DNS线路设置 |
| MaxCacheDiskCapacity | dbs.JSON | maxCacheDiskCapacity |  |  | 硬盘缓存容量 |
| MaxCacheMemoryCapacity | dbs.JSON | maxCacheMemoryCapacity |  |  | 内存缓存容量 |
| CacheDiskDir | string | cacheDiskDir |  |  | 主缓存目录 |
| CacheDiskSubDirs | dbs.JSON | cacheDiskSubDirs |  |  | 其他缓存目录 |
| DnsResolver | dbs.JSON | dnsResolver |  |  | DNS解析器 |
| EnableIPLists | bool | enableIPLists |  |  | 启用IP名单 |
| ApiNodeAddrs | dbs.JSON | apiNodeAddrs |  |  | API节点地址 |
| OfflineDay | string | offlineDay |  |  | 下线日期YYYYMMDD |
| OfflineIsNotified | bool | offlineIsNotified |  |  | 下线是否已通知 |
| IsBackupForCluster | bool | isBackupForCluster |  |  | 是否为集群备用节点 |
| IsBackupForGroup | bool | isBackupForGroup |  |  | 是否为分组备用节点 |
| BackupIPs | dbs.JSON | backupIPs |  |  | 备用IP |
| ActionStatus | dbs.JSON | actionStatus |  |  | 当前动作配置 |
| BypassMobile | int32 | bypassMobile |  |  | 是否过移动 |

## NodeAction

**文件:** `node_action_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| NodeId | uint64 | nodeId |  | Node | 节点ID |
| Role | string | role |  |  | 角色 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Conds | dbs.JSON | conds |  |  | 条件 |
| Action | dbs.JSON | action |  |  | 动作 |
| Duration | dbs.JSON | duration |  |  | 持续时间 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |

## NodeCluster

**文件:** `node_cluster_model.go`

| 字段名                  | 类型       | 标签                   | 主键  | 外键                 | 注释           |
| -------------------- | -------- | -------------------- | --- | ------------------ | ------------ |
| Id                   | uint32   | id                   | ✓   |                    | ID           |
| AdminId              | uint32   | adminId              |     | Admin              | 管理员ID        |
| UserId               | uint32   | userId               |     | User               | 用户ID         |
| IsOn                 | bool     | isOn                 |     |                    | 是否启用         |
| Name                 | string   | name                 |     |                    | 名称           |
| UseAllAPINodes       | uint8    | useAllAPINodes       |     |                    | 是否使用所有API节点  |
| ApiNodes             | dbs.JSON | apiNodes             |     |                    | 使用的API节点     |
| InstallDir           | string   | installDir           |     |                    | 安装目录         |
| Order                | uint32   | order                |     |                    | 排序           |
| CreatedAt            | uint64   | createdAt            |     |                    | 创建时间         |
| GrantId              | uint32   | grantId              |     | Grant              | 默认认证方式       |
| SshParams            | dbs.JSON | sshParams            |     |                    | SSH默认参数      |
| State                | uint8    | state                |     |                    | 状态           |
| AutoRegister         | uint8    | autoRegister         |     |                    | 是否开启自动注册     |
| UniqueId             | string   | uniqueId             |     | Unique             | 唯一ID         |
| Secret               | string   | secret               |     |                    | 密钥           |
| HealthCheck          | dbs.JSON | healthCheck          |     |                    | 健康检查         |
| DnsName              | string   | dnsName              |     |                    | DNS名称        |
| DnsDomainId          | uint32   | dnsDomainId          |     | DnsDomain          | 域名ID         |
| Dns                  | dbs.JSON | dns                  |     |                    | DNS配置        |
| Toa                  | dbs.JSON | toa                  |     |                    | TOA配置        |
| CachePolicyId        | uint32   | cachePolicyId        |     | CachePolicy        | 缓存策略ID       |
| HttpFirewallPolicyId | uint32   | httpFirewallPolicyId |     | HttpFirewallPolicy | WAF策略ID      |
| AccessLog            | dbs.JSON | accessLog            |     |                    | 访问日志设置       |
| SystemServices       | dbs.JSON | systemServices       |     |                    | 系统服务设置       |
| TimeZone             | string   | timeZone             |     |                    | 时区           |
| NodeMaxThreads       | uint32   | nodeMaxThreads       |     |                    | 节点最大线程数      |
| DdosProtection       | dbs.JSON | ddosProtection       |     |                    | DDoS防护设置     |
| AutoOpenPorts        | uint8    | autoOpenPorts        |     |                    | 是否自动尝试开放端口   |
| IsPinned             | bool     | isPinned             |     |                    | 是否置顶         |
| Webp                 | dbs.JSON | webp                 |     |                    | WebP设置       |
| Uam                  | dbs.JSON | uam                  |     |                    | UAM设置        |
| Clock                | dbs.JSON | clock                |     |                    | 时钟配置         |
| GlobalServerConfig   | dbs.JSON | globalServerConfig   |     |                    | 全局服务配置       |
| AutoRemoteStart      | bool     | autoRemoteStart      |     |                    | 自动远程启动       |
| AutoInstallNftables  | bool     | autoInstallNftables  |     |                    | 自动安装nftables |
| IsAD                 | bool     | isAD                 |     |                    | 是否为高防集群      |
| HttpPages            | dbs.JSON | httpPages            |     |                    | 自定义页面设置      |
| Cc                   | dbs.JSON | cc                   |     |                    | CC设置         |
| Http3                | dbs.JSON | http3                |     |                    | HTTP3设置      |
| AutoSystemTuning     | bool     | autoSystemTuning     |     |                    | 是否自动调整系统参数   |
| NetworkSecurity      | dbs.JSON | networkSecurity      |     |                    | 网络安全策略       |
| AutoTrimDisks        | bool     | autoTrimDisks        |     |                    | 是否自动执行TRIM   |
| MaxConcurrentReads   | uint32   | maxConcurrentReads   |     |                    | 节点并发读限制      |
| MaxConcurrentWrites  | uint32   | maxConcurrentWrites  |     |                    | 节点并发写限制      |

## NodeClusterFirewallAction

**文件:** `node_cluster_firewall_action_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| Name | string | name |  |  | 名称 |
| EventLevel | string | eventLevel |  |  | 级别 |
| Type | string | type |  |  | 动作类型 |
| Params | dbs.JSON | params |  |  | 参数 |
| State | uint8 | state |  |  | 状态 |

## NodeClusterMetricItem

**文件:** `node_cluster_metric_item_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| ItemId | uint64 | itemId |  | Item | 指标ID |
| State | uint8 | state |  |  | 是否启用 |

## NodeClusterTrafficDailyStat

**文件:** `node_cluster_traffic_daily_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| Day | string | day |  |  | YYYYMMDD |
| Bytes | uint64 | bytes |  |  | 流量字节 |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存流量 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存的请求数 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击请求数 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |

## NodeGrant

**文件:** `node_grant_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| Name | string | name |  |  | 名称 |
| Method | string | method |  |  | 登录方式 |
| Username | string | username |  |  | 用户名 |
| Password | string | password |  |  | 密码 |
| Su | uint8 | su |  |  | 是否需要su |
| PrivateKey | string | privateKey |  |  | 私钥 |
| Passphrase | string | passphrase |  |  | 私钥密码 |
| Description | string | description |  |  | 备注 |
| NodeId | uint32 | nodeId |  | Node | 专有节点 |
| Role | string | role |  |  | 角色 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## NodeGroup

**文件:** `node_group_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 名称 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| Order | uint32 | order |  |  | 排序 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |

## NodeIPAddress

**文件:** `node_ip_address_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| ClusterIds | dbs.JSON | clusterIds |  |  | 所属集群IDs |
| Role | string | role |  |  | 节点角色 |
| GroupId | uint32 | groupId |  | Group | 所属分组ID |
| Name | string | name |  |  | 名称 |
| Ip | string | ip |  |  | IP地址 |
| Description | string | description |  |  | 描述 |
| State | uint8 | state |  |  | 状态 |
| Order | uint32 | order |  |  | 排序 |
| CanAccess | bool | canAccess |  |  | 是否可以访问 |
| IsOn | bool | isOn |  |  | 是否启用 |
| IsUp | bool | isUp |  |  | 是否上线 |
| IsHealthy | bool | isHealthy |  |  | 是否健康 |
| Thresholds | dbs.JSON | thresholds |  |  | 上线阈值 |
| Connectivity | dbs.JSON | connectivity |  |  | 连通性状态 |
| BackupIP | string | backupIP |  |  | 备用IP |
| BackupThresholdId | uint32 | backupThresholdId |  | BackupThreshold | 触发备用IP的阈值 |
| CountUp | uint32 | countUp |  |  | UP状态次数 |
| CountDown | uint32 | countDown |  |  | DOWN状态次数 |

## NodeIPAddressGroup

**文件:** `node_ip_address_group_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 分组名 |
| Value | string | value |  |  | IP值 |

## NodeIPAddressLog

**文件:** `node_ip_address_log_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AddressId | uint64 | addressId |  | Address | 地址ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| Description | string | description |  |  | 描述 |
| CreatedAt | uint64 | createdAt |  |  | 操作时间 |
| IsUp | bool | isUp |  |  | 是否在线 |
| IsOn | bool | isOn |  |  | 是否启用 |
| CanAccess | bool | canAccess |  |  | 是否可访问 |
| Day | string | day |  |  | YYYYMMDD，用来清理 |
| BackupIP | string | backupIP |  |  | 备用IP |

## NodeIPAddressThreshold

**文件:** `node_ip_address_threshold_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AddressId | uint64 | addressId |  | Address | IP地址ID |
| Items | dbs.JSON | items |  |  | 阈值条目 |
| Actions | dbs.JSON | actions |  |  | 动作 |
| NotifiedAt | uint64 | notifiedAt |  |  | 上次通知时间 |
| IsMatched | bool | isMatched |  |  | 上次是否匹配 |
| State | uint8 | state |  |  | 状态 |
| Order | uint32 | order |  |  | 排序 |

## NodeLog

**文件:** `node_log_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Role | string | role |  |  | 节点角色 |
| Type | string | type |  |  | 类型 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Tag | string | tag |  |  | 标签 |
| Description | string | description |  |  | 描述 |
| Level | string | level |  |  | 级别 |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| Day | string | day |  |  | 日期 |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| OriginId | uint32 | originId |  | Origin | 源站ID |
| Hash | string | hash |  |  | 信息内容Hash |
| Count | uint32 | count |  |  | 重复次数 |
| IsFixed | bool | isFixed |  |  | 是否已处理 |
| IsRead | bool | isRead |  |  | 是否已读 |
| Params | dbs.JSON | params |  |  | 参数 |

## NodeLogin

**文件:** `node_login_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| Role | string | role |  |  | 角色 |
| Name | string | name |  |  | 名称 |
| Type | string | type |  |  | 类型：ssh,agent |
| Params | dbs.JSON | params |  |  | 配置参数 |
| State | uint8 | state |  |  | 状态 |

## NodePriceItem

**文件:** `node_price_item_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Type | string | type |  |  | 类型：峰值|流量 |
| Name | string | name |  |  | 名称 |
| BitsFrom | uint64 | bitsFrom |  |  | 起始值 |
| BitsTo | uint64 | bitsTo |  |  | 结束值 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |

## NodeRegion

**文件:** `node_region_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| Order | uint32 | order |  |  | 排序 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Prices | dbs.JSON | prices |  |  | 流量价格 |
| State | uint8 | state |  |  | 状态 |

## NodeTask

**文件:** `node_task_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Role | string | role |  |  | 节点角色 |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| ServerId | uint64 | serverId |  | Server | 服务ID |
| UserId | uint64 | userId |  | User | 用户ID |
| Type | string | type |  |  | 任务类型 |
| UniqueId | string | uniqueId |  | Unique | 唯一ID：nodeId@type |
| UpdatedAt | uint64 | updatedAt |  |  | 修改时间 |
| IsDone | bool | isDone |  |  | 是否已完成 |
| IsOk | bool | isOk |  |  | 是否已完成 |
| Error | string | error |  |  | 错误信息 |
| IsNotified | bool | isNotified |  |  | 是否已通知更新 |
| Version | uint64 | version |  |  | 版本 |

## NodeThreshold

**文件:** `node_threshold_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Role | string | role |  |  | 节点角色 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Item | string | item |  |  | 监控项 |
| Param | string | param |  |  | 参数 |
| Operator | string | operator |  |  | 操作符 |
| Value | dbs.JSON | value |  |  | 对比值 |
| Message | string | message |  |  | 消息内容 |
| NotifyDuration | uint32 | notifyDuration |  |  | 通知间隔 |
| NotifiedAt | uint32 | notifiedAt |  |  | 上次通知时间 |
| Duration | uint32 | duration |  |  | 时间段 |
| DurationUnit | string | durationUnit |  |  | 时间段单位 |
| SumMethod | string | sumMethod |  |  | 聚合方法 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |

## NodeTrafficDailyStat

**文件:** `node_traffic_daily_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Role | string | role |  |  | 节点角色 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 集群ID |
| Day | string | day |  |  | YYYYMMDD |
| Bytes | uint64 | bytes |  |  | 流量字节 |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存流量 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存的请求数 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击数 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |

## NodeTrafficHourlyStat

**文件:** `node_traffic_hourly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Role | string | role |  |  | 节点角色 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 集群ID |
| Hour | string | hour |  |  | YYYYMMDDHH |
| Bytes | uint64 | bytes |  |  | 流量字节 |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存流量 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存的请求数 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击请求数 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |

## NodeValue

**文件:** `node_value_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| Role | string | role |  |  | 节点角色 |
| Item | string | item |  |  | 监控项 |
| Value | dbs.JSON | value |  |  | 数据 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Day | string | day |  |  | 日期 |
| Hour | string | hour |  |  | 小时 |
| Minute | string | minute |  |  | 分钟 |

## OrderMethod

**文件:** `order_method_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 名称 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Description | string | description |  |  | 描述 |
| ParentCode | string | parentCode |  |  | 内置的父级代号 |
| Code | string | code |  |  | 代号 |
| Url | string | url |  |  | URL |
| Secret | string | secret |  |  | 密钥 |
| Params | dbs.JSON | params |  |  | 参数 |
| ClientType | string | clientType |  |  | 客户端类型 |
| QrcodeTitle | string | qrcodeTitle |  |  | 二维码标题 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |

## Origin

**文件:** `origin_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| ReverseProxyId | uint64 | reverseProxyId |  | ReverseProxy | 所属反向代理ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Version | uint32 | version |  |  | 版本 |
| Addr | dbs.JSON | addr |  |  | 地址 |
| Oss | dbs.JSON | oss |  |  | OSS配置 |
| Description | string | description |  |  | 描述 |
| Code | string | code |  |  | 代号 |
| Weight | uint32 | weight |  |  | 权重 |
| ConnTimeout | dbs.JSON | connTimeout |  |  | 连接超时 |
| ReadTimeout | dbs.JSON | readTimeout |  |  | 读超时 |
| IdleTimeout | dbs.JSON | idleTimeout |  |  | 空闲连接超时 |
| MaxFails | uint32 | maxFails |  |  | 最多失败次数 |
| MaxConns | uint32 | maxConns |  |  | 最大并发连接数 |
| MaxIdleConns | uint32 | maxIdleConns |  |  | 最多空闲连接数 |
| HttpRequestURI | string | httpRequestURI |  |  | 转发后的请求URI |
| HttpRequestHeader | dbs.JSON | httpRequestHeader |  |  | 请求Header配置 |
| HttpResponseHeader | dbs.JSON | httpResponseHeader |  |  | 响应Header配置 |
| Host | string | host |  |  | 自定义主机名 |
| HealthCheck | dbs.JSON | healthCheck |  |  | 健康检查设置 |
| Cert | dbs.JSON | cert |  |  | 证书设置 |
| Ftp | dbs.JSON | ftp |  |  | FTP相关设置 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Domains | dbs.JSON | domains |  |  | 所属域名 |
| FollowPort | bool | followPort |  |  | 端口跟随 |
| State | uint8 | state |  |  | 状态 |
| Http2Enabled | bool | http2Enabled |  |  | 是否支持HTTP/2 |

## Plan

**文件:** `plan_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 套餐名 |
| Description | string | description |  |  | 套餐简介 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| TrafficLimit | dbs.JSON | trafficLimit |  |  | 流量限制 |
| BandwidthLimitPerNode | dbs.JSON | bandwidthLimitPerNode |  |  | 单节点带宽限制 |
| Features | dbs.JSON | features |  |  | 允许的功能 |
| HasFullFeatures | bool | hasFullFeatures |  |  | 是否有完整的功能 |
| TrafficPrice | dbs.JSON | trafficPrice |  |  | 流量价格设定 |
| BandwidthPrice | dbs.JSON | bandwidthPrice |  |  | 带宽价格 |
| MonthlyPrice | float64 | monthlyPrice |  |  | 月付 |
| SeasonallyPrice | float64 | seasonallyPrice |  |  | 季付 |
| YearlyPrice | float64 | yearlyPrice |  |  | 年付 |
| PriceType | string | priceType |  |  | 价格类型 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |
| TotalServers | uint32 | totalServers |  |  | 可以绑定的网站数量 |
| TotalServerNamesPerServer | uint32 | totalServerNamesPerServer |  |  | 每个网站可以绑定的域名数量 |
| TotalServerNames | uint32 | totalServerNames |  |  | 总域名数量 |
| MonthlyRequests | uint64 | monthlyRequests |  |  | 每月访问量额度 |
| DailyRequests | uint64 | dailyRequests |  |  | 每日访问量额度 |
| DailyWebsocketConnections | uint64 | dailyWebsocketConnections |  |  | 每日Websocket连接数 |
| MonthlyWebsocketConnections | uint64 | monthlyWebsocketConnections |  |  | 每月Websocket连接数 |
| MaxUploadSize | dbs.JSON | maxUploadSize |  |  | 最大上传 |

## Post

**文件:** `post_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| CategoryId | uint32 | categoryId |  | Category | 文章分类 |
| Type | string | type |  |  | 类型：normal, url |
| Url | string | url |  |  | URL |
| Subject | string | subject |  |  | 标题 |
| Body | string | body |  |  | 内容 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| IsPublished | bool | isPublished |  |  | 是否已发布 |
| PublishedAt | uint64 | publishedAt |  |  | 发布时间 |
| ProductCode | string | productCode |  |  | 产品代号 |
| State | uint8 | state |  |  | 状态 |

## PostCategory

**文件:** `post_category_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 分类名称 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Code | string | code |  |  | 代号 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 分类状态 |

## Provider

**文件:** `provider_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Username | string | username |  |  | 用户名 |
| Password | string | password |  |  | 密码 |
| Fullname | string | fullname |  |  | 真实姓名 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| UpdatedAt | uint64 | updatedAt |  |  | 修改时间 |
| State | uint8 | state |  |  | 状态 |

## RegionCity

**文件:** `region_city_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id1 | uint32 | id |  |  | ID |
| ValueId | uint32 | valueId |  | Value | 实际ID |
| ProvinceId | uint32 | provinceId |  | Province | 省份ID |
| Name | string | name |  |  | 名称 |
| Codes | dbs.JSON | codes |  |  | 代号 |
| CustomName | string | customName |  |  | 自定义名称 |
| CustomCodes | dbs.JSON | customCodes |  |  | 自定义代号 |
| State | uint8 | state |  |  | 状态 |
| DataId | string | dataId |  | Data | 原始数据ID |

## RegionCountry

**文件:** `region_country_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| ValueId | uint32 | valueId |  | Value | 实际ID |
| ValueCode | string | valueCode |  |  | 值代号 |
| Name | string | name |  |  | 名称 |
| Codes | dbs.JSON | codes |  |  | 代号 |
| CustomName | string | customName |  |  | 自定义名称 |
| CustomCodes | dbs.JSON | customCodes |  |  | 自定义代号 |
| State | uint8 | state |  |  | 状态 |
| DataId | string | dataId |  | Data | 原始数据ID |
| Pinyin | dbs.JSON | pinyin |  |  | 拼音 |
| IsCommon | bool | isCommon |  |  | 是否常用 |
| RouteCode | string | routeCode |  |  | 线路代号 |

## RegionProvider

**文件:** `region_provider_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id1 | uint32 | id |  |  | ID |
| ValueId | uint32 | valueId |  | Value | 实际ID |
| Name | string | name |  |  | 名称 |
| Codes | dbs.JSON | codes |  |  | 代号 |
| CustomName | string | customName |  |  | 自定义名称 |
| CustomCodes | dbs.JSON | customCodes |  |  | 自定义代号 |
| State | uint8 | state |  |  | 状态 |

## RegionProvince

**文件:** `region_province_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| ValueId | uint32 | valueId |  | Value | 实际ID |
| CountryId | uint32 | countryId |  | Country | 国家ID |
| Name | string | name |  |  | 名称 |
| Codes | dbs.JSON | codes |  |  | 代号 |
| CustomName | string | customName |  |  | 自定义名称 |
| CustomCodes | dbs.JSON | customCodes |  |  | 自定义代号 |
| State | uint8 | state |  |  | 状态 |
| DataId | string | dataId |  | Data | 原始数据ID |
| RouteCode | string | routeCode |  |  | 线路代号 |

## RegionTown

**文件:** `region_town_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id1 | uint32 | id |  |  | ID |
| ValueId | uint32 | valueId |  | Value | 真实ID |
| CityId | uint32 | cityId |  | City | 城市ID |
| Name | string | name |  |  | 名称 |
| Codes | dbs.JSON | codes |  |  | 代号 |
| CustomName | string | customName |  |  | 自定义名称 |
| CustomCodes | dbs.JSON | customCodes |  |  | 自定义代号 |
| State | uint8 | state |  |  | 状态 |
| DataId | string | dataId |  | Data | 原始数据ID |

## ReportNode

**文件:** `report_node_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| UniqueId | string | uniqueId |  | Unique | 唯一ID |
| Secret | string | secret |  |  | 密钥 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Location | string | location |  |  | 所在区域 |
| Isp | string | isp |  |  | 网络服务商 |
| AllowIPs | dbs.JSON | allowIPs |  |  | 允许的IP |
| IsActive | bool | isActive |  |  | 是否活跃 |
| Status | dbs.JSON | status |  |  | 状态 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| GroupIds | dbs.JSON | groupIds |  |  | 分组ID |

## ReportNodeGroup

**文件:** `report_node_group_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Name | string | name |  |  | 名称 |
| State | uint8 | state |  |  | 状态 |
| IsOn | bool | isOn |  |  | 是否启用 |

## ReportResult

**文件:** `report_result_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Type | string | type |  |  | 对象类型 |
| TargetId | uint64 | targetId |  | Target | 对象ID |
| TargetDesc | string | targetDesc |  |  | 对象描述 |
| UpdatedAt | uint64 | updatedAt |  |  | 更新时间 |
| ReportNodeId | uint32 | reportNodeId |  | ReportNode | 监控节点ID |
| IsOk | bool | isOk |  |  | 是否可连接 |
| Level | string | level |  |  | 级别 |
| CostMs | float64 | costMs |  |  | 单次连接花费的时间 |
| Error | string | error |  |  | 产生的错误信息 |
| CountUp | uint32 | countUp |  |  | 连续上线次数 |
| CountDown | uint32 | countDown |  |  | 连续下线次数 |

## ReverseProxy

**文件:** `reverse_proxy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| TemplateId | uint32 | templateId |  | Template | 模版ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Scheduling | dbs.JSON | scheduling |  |  | 调度算法 |
| PrimaryOrigins | dbs.JSON | primaryOrigins |  |  | 主要源站 |
| BackupOrigins | dbs.JSON | backupOrigins |  |  | 备用源站 |
| StripPrefix | string | stripPrefix |  |  | 去除URL前缀 |
| RequestHostType | uint8 | requestHostType |  |  | 请求Host类型 |
| RequestHost | string | requestHost |  |  | 请求Host |
| RequestHostExcludingPort | bool | requestHostExcludingPort |  |  | 移除请求Host中的域名 |
| RequestURI | string | requestURI |  |  | 请求URI |
| AutoFlush | uint8 | autoFlush |  |  | 是否自动刷新缓冲区 |
| AddHeaders | dbs.JSON | addHeaders |  |  | 自动添加的Header列表 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| ConnTimeout | dbs.JSON | connTimeout |  |  | 连接超时时间 |
| ReadTimeout | dbs.JSON | readTimeout |  |  | 读取超时时间 |
| IdleTimeout | dbs.JSON | idleTimeout |  |  | 空闲超时时间 |
| MaxConns | uint32 | maxConns |  |  | 最大并发连接数 |
| MaxIdleConns | uint32 | maxIdleConns |  |  | 最大空闲连接数 |
| ProxyProtocol | dbs.JSON | proxyProtocol |  |  | Proxy Protocol配置 |
| FollowRedirects | uint8 | followRedirects |  |  | 回源跟随 |
| Retry50X | bool | retry50X |  |  | 启用50X重试 |
| Retry40X | bool | retry40X |  |  | 启用40X重试 |

## SSLCert

**文件:** `ssl_cert_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| UpdatedAt | uint64 | updatedAt |  |  | 修改时间 |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 证书名 |
| Description | string | description |  |  | 描述 |
| CertData | []byte | certData |  |  | 证书内容 |
| KeyData | []byte | keyData |  |  | 密钥内容 |
| ServerName | string | serverName |  |  | 证书使用的主机名 |
| IsCA | bool | isCA |  |  | 是否为CA证书 |
| GroupIds | dbs.JSON | groupIds |  |  | 证书分组 |
| TimeBeginAt | uint64 | timeBeginAt |  |  | 开始时间 |
| TimeEndAt | uint64 | timeEndAt |  |  | 结束时间 |
| DnsNames | dbs.JSON | dnsNames |  |  | DNS名称列表 |
| CommonNames | dbs.JSON | commonNames |  |  | 发行单位列表 |
| IsACME | bool | isACME |  |  | 是否为ACME自动生成的 |
| AcmeTaskId | uint64 | acmeTaskId |  | AcmeTask | ACME任务ID |
| NotifiedAt | uint64 | notifiedAt |  |  | 最后通知时间 |
| Ocsp | []byte | ocsp |  |  | OCSP缓存 |
| OcspIsUpdated | uint8 | ocspIsUpdated |  |  | OCSP是否已更新 |
| OcspUpdatedAt | uint64 | ocspUpdatedAt |  |  | OCSP更新时间 |
| OcspError | string | ocspError |  |  | OCSP更新错误 |
| OcspUpdatedVersion | uint64 | ocspUpdatedVersion |  |  | OCSP更新版本 |
| OcspExpiresAt | uint64 | ocspExpiresAt |  |  | OCSP过期时间(UTC) |
| OcspTries | uint32 | ocspTries |  |  | OCSP尝试次数 |

## SSLCertGroup

**文件:** `ssl_cert_group_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Name | string | name |  |  | 分组名 |
| Order | uint32 | order |  |  | 分组排序 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## SSLPolicy

**文件:** `ssl_policy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Certs | dbs.JSON | certs |  |  | 证书列表 |
| ClientCACerts | dbs.JSON | clientCACerts |  |  | 客户端证书 |
| ClientAuthType | uint32 | clientAuthType |  |  | 客户端认证类型 |
| MinVersion | string | minVersion |  |  | 支持的SSL最小版本 |
| CipherSuitesIsOn | uint8 | cipherSuitesIsOn |  |  | 是否自定义加密算法套件 |
| CipherSuites | dbs.JSON | cipherSuites |  |  | 加密算法套件 |
| Hsts | dbs.JSON | hsts |  |  | HSTS设置 |
| Http2Enabled | bool | http2Enabled |  |  | 是否启用HTTP/2 |
| Http3Enabled | bool | http3Enabled |  |  | 是否启用HTTP/3 |
| OcspIsOn | uint8 | ocspIsOn |  |  | 是否启用OCSP |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## Script

**文件:** `script_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Filename | string | filename |  |  | 文件名 |
| Code | string | code |  |  | 代码 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| UpdatedAt | uint64 | updatedAt |  |  | 修改时间 |
| State | uint8 | state |  |  | 是否启用 |

## ScriptHistory

**文件:** `script_history_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| ScriptId | uint64 | scriptId |  | Script | 脚本ID |
| Filename | string | filename |  |  | 文件名 |
| Code | string | code |  |  | 代码 |
| Version | uint64 | version |  |  | 版本号 |

## Server

**文件:** `server_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| UserId | uint32 | userId |  | User | 用户ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| Type | string | type |  |  | 服务类型 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| PlainServerNames | dbs.JSON | plainServerNames |  |  | 扁平化域名列表 |
| ServerNames | dbs.JSON | serverNames |  |  | 域名列表 |
| AuditingAt | uint64 | auditingAt |  |  | 审核提交时间 |
| AuditingServerNames | dbs.JSON | auditingServerNames |  |  | 审核中的域名 |
| IsAuditing | bool | isAuditing |  |  | 是否正在审核 |
| AuditingResult | dbs.JSON | auditingResult |  |  | 审核结果 |
| Http | dbs.JSON | http |  |  | HTTP配置 |
| Https | dbs.JSON | https |  |  | HTTPS配置 |
| Tcp | dbs.JSON | tcp |  |  | TCP配置 |
| Tls | dbs.JSON | tls |  |  | TLS配置 |
| Unix | dbs.JSON | unix |  |  | Unix配置 |
| Udp | dbs.JSON | udp |  |  | UDP配置 |
| WebId | uint32 | webId |  | Web | WEB配置 |
| ReverseProxy | dbs.JSON | reverseProxy |  |  | 反向代理配置 |
| GroupIds | dbs.JSON | groupIds |  |  | 分组ID列表 |
| Config | dbs.JSON | config |  |  | 服务配置，自动生成 |
| ConfigMd5 | string | configMd5 |  |  | Md5 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| IncludeNodes | dbs.JSON | includeNodes |  |  | 部署条件 |
| ExcludeNodes | dbs.JSON | excludeNodes |  |  | 节点排除条件 |
| Version | uint32 | version |  |  | 版本号 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |
| DnsName | string | dnsName |  |  | DNS名称 |
| TcpPorts | dbs.JSON | tcpPorts |  |  | 所包含TCP端口 |
| UdpPorts | dbs.JSON | udpPorts |  |  | 所包含UDP端口 |
| SupportCNAME | uint8 | supportCNAME |  |  | 允许CNAME不在域名名单 |
| TrafficLimit | dbs.JSON | trafficLimit |  |  | 流量限制 |
| TrafficDay | string | trafficDay |  |  | YYYYMMDD |
| TrafficMonth | string | trafficMonth |  |  | YYYYMM |
| TotalDailyTraffic | float64 | totalDailyTraffic |  |  | 日流量 |
| TotalMonthlyTraffic | float64 | totalMonthlyTraffic |  |  | 月流量 |
| TrafficLimitStatus | dbs.JSON | trafficLimitStatus |  |  | 流量限制状态 |
| TotalTraffic | float64 | totalTraffic |  |  | 总流量 |
| UserPlanId | uint32 | userPlanId |  | UserPlan | 所属套餐ID |
| LastUserPlanId | uint32 | lastUserPlanId |  | LastUserPlan | 上一次使用的套餐 |
| Uam | dbs.JSON | uam |  |  | UAM设置 |
| BandwidthTime | string | bandwidthTime |  |  | 带宽更新时间，YYYYMMDDHHII |
| BandwidthBytes | uint64 | bandwidthBytes |  |  | 最近带宽峰值 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 最近攻击请求数 |
| CountRequests | uint64 | countRequests |  |  | 最近总请求数 |

## ServerBandwidthStat

**文件:** `server_bandwidth_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| ServerId | uint64 | serverId |  | Server | 服务ID |
| RegionId | uint32 | regionId |  | Region | 区域ID |
| UserPlanId | uint64 | userPlanId |  | UserPlan | 用户套餐ID |
| Day | string | day |  |  | 日期YYYYMMDD |
| TimeAt | string | timeAt |  |  | 时间点HHMM |
| Bytes | uint64 | bytes |  |  | 带宽字节 |
| AvgBytes | uint64 | avgBytes |  |  | 平均流量 |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存的流量 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存的请求数 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击请求数 |
| TotalBytes | uint64 | totalBytes |  |  | 总流量 |
| CountIPs | uint64 | countIPs |  |  | 独立IP |

## ServerBill

**文件:** `server_bill_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint32 | userId |  | User | 用户ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| Amount | float64 | amount |  |  | 金额 |
| Month | string | month |  |  | 月份 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| UserPlanId | uint32 | userPlanId |  | UserPlan | 用户套餐ID |
| PlanId | uint32 | planId |  | Plan | 套餐ID |
| TotalTrafficBytes | uint64 | totalTrafficBytes |  |  | 总流量 |
| BandwidthPercentileBytes | uint64 | bandwidthPercentileBytes |  |  | 带宽百分位字节 |
| BandwidthPercentile | uint8 | bandwidthPercentile |  |  | 带宽百分位 |
| PriceType | string | priceType |  |  | 计费类型 |

## ServerClientBrowserMonthlyStat

**文件:** `server_client_browser_monthly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| BrowserId | uint32 | browserId |  | Browser | 浏览器ID |
| Month | string | month |  |  | YYYYMM |
| Version | string | version |  |  | 主版本号 |
| Count | uint64 | count |  |  | 数量 |

## ServerClientSystemMonthlyStat

**文件:** `server_client_system_monthly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| SystemId | uint32 | systemId |  | System | 系统ID |
| Version | string | version |  |  | 主版本号 |
| Month | string | month |  |  | YYYYMM |
| Count | uint64 | count |  |  | 数量 |

## ServerDailyStat

**文件:** `server_daily_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint32 | userId |  | User | 用户ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| RegionId | uint32 | regionId |  | Region | 区域ID |
| Bytes | uint64 | bytes |  |  | 流量 |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存的流量 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存的请求数 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击请求数 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |
| Day | string | day |  |  | 日期YYYYMMDD |
| Hour | string | hour |  |  | YYYYMMDDHH |
| TimeFrom | string | timeFrom |  |  | 开始时间HHMMSS |
| TimeTo | string | timeTo |  |  | 结束时间 |
| IsCharged | bool | isCharged |  |  | 是否已计算费用 |
| PlanId | uint64 | planId |  | Plan | 套餐ID |
| Fee | float64 | fee |  |  | 费用 |

## ServerDomainHourlyStat

**文件:** `server_domain_hourly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| NodeId | uint32 | nodeId |  | Node | 节点ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| Domain | string | domain |  |  | 域名 |
| Hour | string | hour |  |  | YYYYMMDDHH |
| Bytes | uint64 | bytes |  |  | 流量 |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存流量 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存请求 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击请求数 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |

## ServerGroup

**文件:** `server_group_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Order | uint32 | order |  |  | 排序 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |
| HttpReverseProxy | dbs.JSON | httpReverseProxy |  |  | 反向代理设置 |
| TcpReverseProxy | dbs.JSON | tcpReverseProxy |  |  | TCP反向代理 |
| UdpReverseProxy | dbs.JSON | udpReverseProxy |  |  | UDP反向代理 |
| WebId | uint32 | webId |  | Web | Web配置ID |

## ServerHTTPFirewallDailyStat

**文件:** `server_http_firewall_daily_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| Day | string | day |  |  | YYYYMMDD |
| HttpFirewallRuleGroupId | uint32 | httpFirewallRuleGroupId |  | HttpFirewallRuleGroup | WAF分组ID |
| Action | string | action |  |  | 采取的动作 |
| Count | uint64 | count |  |  | 数量 |

## ServerHTTPFirewallHourlyStat

**文件:** `server_http_firewall_hourly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| Day | string | day |  |  | YYYYMMDD |
| Hour | string | hour |  |  | YYYYMMDDHH |
| HttpFirewallRuleGroupId | uint32 | httpFirewallRuleGroupId |  | HttpFirewallRuleGroup | WAF分组ID |
| Action | string | action |  |  | 采取的动作 |
| Count | uint64 | count |  |  | 数量 |

## ServerRegionCityMonthlyStat

**文件:** `server_region_city_monthly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| CityId | uint32 | cityId |  | City | 城市ID |
| Month | string | month |  |  | 月份YYYYMM |
| Count | uint64 | count |  |  | 数量 |

## ServerRegionCountryDailyStat

**文件:** `server_region_country_daily_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| CountryId | uint32 | countryId |  | Country | 国家/区域ID |
| Day | string | day |  |  | 日期YYYYMMDD |
| CountRequests | uint64 | countRequests |  |  | 请求数量 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击数量 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |
| Bytes | uint64 | bytes |  |  | 总流量 |

## ServerRegionCountryMonthlyStat

**文件:** `server_region_country_monthly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| CountryId | uint32 | countryId |  | Country | 国家/区域ID |
| Month | string | month |  |  | 月份YYYYMM |
| Count | uint64 | count |  |  | 数量 |

## ServerRegionProviderMonthlyStat

**文件:** `server_region_provider_monthly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| ProviderId | uint32 | providerId |  | Provider | 运营商ID |
| Month | string | month |  |  | 月份YYYYMM |
| Count | uint64 | count |  |  | 数量 |

## ServerRegionProvinceMonthlyStat

**文件:** `server_region_province_monthly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ServerId | uint32 | serverId |  | Server | 服务ID |
| ProvinceId | uint32 | provinceId |  | Province | 省份ID |
| Month | string | month |  |  | 月份YYYYMM |
| Count | uint64 | count |  |  | 数量 |

## ServerStatBoard

**文件:** `server_stat_board_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Name | string | name |  |  | 名称 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |

## ServerStatBoardChart

**文件:** `server_stat_board_chart_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| BoardId | uint64 | boardId |  | Board | 看板ID |
| Code | string | code |  |  | 内置图表代码 |
| ItemId | uint32 | itemId |  | Item | 指标ID |
| ChartId | uint32 | chartId |  | Chart | 图表ID |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |

## SubUser

**文件:** `sub_user_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| UserId | uint32 | userId |  | User | 所属主用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| Username | string | username |  |  | 用户名 |
| Password | string | password |  |  | 密码 |
| State | uint8 | state |  |  | 状态 |

## SysEvent

**文件:** `sys_event_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Type | string | type |  |  | 类型 |
| Params | dbs.JSON | params |  |  | 参数 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## SysLocker

**文件:** `sys_locker_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Key | string | key |  |  | 键值 |
| Version | uint64 | version |  |  | 版本号 |
| TimeoutAt | uint64 | timeoutAt |  |  | 超时时间 |

## SysSetting

**文件:** `sys_setting_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Code | string | code |  |  | 代号 |
| Value | dbs.JSON | value |  |  | 配置值 |

## TCPFirewallPolicy

**文件:** `tcp_firewall_policy_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | int32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| TemplateId | uint32 | templateId |  | Template | 模版ID |

## TrafficDailyStat

**文件:** `traffic_daily_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Day | string | day |  |  | YYYYMMDD |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存流量 |
| Bytes | uint64 | bytes |  |  | 流量字节 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存请求数 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击量 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |
| CountIPs | uint64 | countIPs |  |  | 独立IP数 |

## TrafficHourlyStat

**文件:** `traffic_hourly_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Hour | string | hour |  |  | YYYYMMDDHH |
| Bytes | uint64 | bytes |  |  | 流量字节 |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存流量 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存请求数 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击数 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |

## TrafficPackage

**文件:** `traffic_package_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Size | uint32 | size |  |  | 尺寸 |
| Unit | string | unit |  |  | 单位（gb|tb等） |
| Bytes | uint64 | bytes |  |  | 字节 |
| IsOn | bool | isOn |  |  | 是否启用 |
| State | uint8 | state |  |  | 状态 |

## TrafficPackagePeriod

**文件:** `traffic_package_period_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Count | uint32 | count |  |  | 数量 |
| Unit | string | unit |  |  | 单位：month, year |
| Months | uint32 | months |  |  | 月数 |
| State | uint8 | state |  |  | 状态 |

## TrafficPackagePrice

**文件:** `traffic_package_price_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| PackageId | uint32 | packageId |  | Package | 套餐ID |
| RegionId | uint32 | regionId |  | Region | 区域ID |
| PeriodId | uint32 | periodId |  | Period | 有效期ID |
| Price | float64 | price |  |  | 价格 |
| DiscountPrice | float64 | discountPrice |  |  | 折后价格 |

## UpdatingServerList

**文件:** `updating_server_list_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| UniqueId | string | uniqueId |  | Unique | 唯一ID |
| ServerIds | dbs.JSON | serverIds |  |  | 服务IDs |
| Day | string | day |  |  | 创建日期 |

## User

**文件:** `user_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Username | string | username |  |  | 用户名 |
| Password | string | password |  |  | 密码 |
| Fullname | string | fullname |  |  | 真实姓名 |
| Mobile | string | mobile |  |  | 手机号 |
| VerifiedMobile | string | verifiedMobile |  |  | 已验证手机号 |
| MobileIsVerified | uint8 | mobileIsVerified |  |  | 手机号是否已验证 |
| Tel | string | tel |  |  | 联系电话 |
| Remark | string | remark |  |  | 备注 |
| Email | string | email |  |  | 邮箱地址 |
| VerifiedEmail | string | verifiedEmail |  |  | 激活后的邮箱 |
| EmailIsVerified | uint8 | emailIsVerified |  |  | 邮箱是否已验证 |
| AvatarFileId | uint64 | avatarFileId |  | AvatarFile | 头像文件ID |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Day | string | day |  |  | YYYYMMDD |
| UpdatedAt | uint64 | updatedAt |  |  | 修改时间 |
| State | uint8 | state |  |  | 状态 |
| Source | string | source |  |  | 来源 |
| ClusterId | uint32 | clusterId |  | Cluster | 集群ID |
| Features | dbs.JSON | features |  |  | 允许操作的特征 |
| RegisteredIP | string | registeredIP |  |  | 注册使用的IP |
| IsRejected | bool | isRejected |  |  | 是否已拒绝 |
| RejectReason | string | rejectReason |  |  | 拒绝理由 |
| IsVerified | bool | isVerified |  |  | 是否验证通过 |
| RequirePlans | uint8 | requirePlans |  |  | 是否需要购买套餐 |
| Modules | dbs.JSON | modules |  |  | 用户模块 |
| PriceType | string | priceType |  |  | 计费类型：traffic|bandwidth |
| PricePeriod | string | pricePeriod |  |  | 结算周期 |
| ServersEnabled | uint8 | serversEnabled |  |  | 是否禁用所有服务 |
| Notification | dbs.JSON | notification |  |  | 通知设置 |
| BandwidthAlgo | string | bandwidthAlgo |  |  | 带宽算法 |
| BandwidthModifier | float64 | bandwidthModifier |  |  | 带宽修正值 |
| Lang | string | lang |  |  | 语言代号 |

## UserADInstance

**文件:** `user_ad_instance_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint64 | userId |  | User | 用户ID |
| InstanceId | uint32 | instanceId |  | Instance | 高防实例ID |
| PeriodId | uint32 | periodId |  | Period | 有效期 |
| PeriodCount | uint32 | periodCount |  |  | 有效期数量 |
| PeriodUnit | string | periodUnit |  |  | 有效期单位 |
| DayFrom | string | dayFrom |  |  | 开始日期 |
| DayTo | string | dayTo |  |  | 结束日期 |
| MaxObjects | uint32 | maxObjects |  |  | 最多防护对象数 |
| ObjectCodes | dbs.JSON | objectCodes |  |  | 防护对象 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |

## UserAccessKey

**文件:** `user_access_key_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint32 | userId |  | User | 用户ID |
| SubUserId | uint32 | subUserId |  | SubUser | 子用户ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| UniqueId | string | uniqueId |  | Unique | 唯一的Key |
| Secret | string | secret |  |  | 密钥 |
| Description | string | description |  |  | 备注 |
| AccessedAt | uint64 | accessedAt |  |  | 最近一次访问时间 |
| State | uint8 | state |  |  | 状态 |

## UserAccount

**文件:** `user_account_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| Total | float64 | total |  |  | 可用总余额 |
| TotalFrozen | float64 | totalFrozen |  |  | 冻结余额 |

## UserAccountDailyStat

**文件:** `user_account_daily_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| Day | string | day |  |  | YYYYMMDD |
| Month | string | month |  |  | YYYYMM |
| Income | float64 | income |  |  | 收入 |
| Expense | float64 | expense |  |  | 支出 |

## UserAccountLog

**文件:** `user_account_log_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| AccountId | uint64 | accountId |  | Account | 账户ID |
| Delta | float64 | delta |  |  | 操作余额的数量（可为负） |
| DeltaFrozen | float64 | deltaFrozen |  |  | 操作冻结的数量（可为负） |
| Total | float64 | total |  |  | 操作后余额 |
| TotalFrozen | float64 | totalFrozen |  |  | 操作后冻结余额 |
| EventType | string | eventType |  |  | 类型 |
| Description | string | description |  |  | 描述文字 |
| Day | string | day |  |  | YYYYMMDD |
| CreatedAt | uint64 | createdAt |  |  | 时间 |
| Params | dbs.JSON | params |  |  | 参数 |

## UserBandwidthStat

**文件:** `user_bandwidth_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| RegionId | uint32 | regionId |  | Region | 区域ID |
| Day | string | day |  |  | 日期YYYYMMDD |
| TimeAt | string | timeAt |  |  | 时间点HHII |
| Bytes | uint64 | bytes |  |  | 带宽 |
| TotalBytes | uint64 | totalBytes |  |  | 总流量 |
| AvgBytes | uint64 | avgBytes |  |  | 平均流量 |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存的流量 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存的请求数 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击请求数 |

## UserBill

**文件:** `user_bill_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint32 | userId |  | User | 用户ID |
| Type | string | type |  |  | 消费类型 |
| PricePeriod | string | pricePeriod |  |  | 计费周期 |
| Description | string | description |  |  | 描述 |
| Amount | float64 | amount |  |  | 消费数额 |
| DayFrom | string | dayFrom |  |  | YYYYMMDD |
| DayTo | string | dayTo |  |  | YYYYMMDD |
| Month | string | month |  |  | 帐期YYYYMM |
| CanPay | bool | canPay |  |  | 是否可以支付 |
| IsPaid | bool | isPaid |  | IsPa | 是否已支付 |
| PaidAt | uint64 | paidAt |  |  | 支付时间 |
| Code | string | code |  |  | 账单编号 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| CreatedDay | string | createdDay |  |  | 创建日期 |
| State | uint8 | state |  |  | 状态 |

## UserEmailNotification

**文件:** `user_email_notification_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Email | string | email |  |  | 邮箱地址 |
| Subject | string | subject |  |  | 标题 |
| Body | string | body |  |  | 内容 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| Day | string | day |  |  | YYYYMMDD |

## UserEmailVerification

**文件:** `user_email_verification_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Email | string | email |  |  | 邮箱 |
| UserId | uint64 | userId |  | User | 用户ID |
| Code | string | code |  |  | 激活码 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| IsSent | bool | isSent |  |  | 是否已发送 |
| IsVerified | bool | isVerified |  |  | 是否已激活 |
| Day | string | day |  |  | YYYYMMDD |

## UserIdentity

**文件:** `user_identity_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| OrgType | string | orgType |  |  | 组织类型 |
| Type | string | type |  |  | 证件类型 |
| RealName | string | realName |  |  | 真实姓名 |
| Number | string | number |  |  | 编号 |
| FileIds | dbs.JSON | fileIds |  |  | 文件ID |
| Status | string | status |  |  | 状态：none,submitted,verified,rejected |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| UpdatedAt | uint64 | updatedAt |  |  | 修改时间 |
| SubmittedAt | uint64 | submittedAt |  |  | 提交时间 |
| RejectedAt | uint64 | rejectedAt |  |  | 拒绝时间 |
| VerifiedAt | uint64 | verifiedAt |  |  | 认证时间 |
| RejectReason | string | rejectReason |  |  | 拒绝原因 |

## UserMobileVerification

**文件:** `user_mobile_verification_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Mobile | string | mobile |  |  | 手机号码 |
| UserId | uint64 | userId |  | User | 用户ID |
| Code | string | code |  |  | 激活码 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| IsSent | bool | isSent |  |  | 是否已发送 |
| IsVerified | bool | isVerified |  |  | 是否已激活 |
| Day | string | day |  |  | YYYYMMDD |

## UserNode

**文件:** `user_node_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint32 | id | ✓ |  | ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| UniqueId | string | uniqueId |  | Unique | 唯一ID |
| Secret | string | secret |  |  | 密钥 |
| Name | string | name |  |  | 名称 |
| Description | string | description |  |  | 描述 |
| Http | dbs.JSON | http |  |  | 监听的HTTP配置 |
| Https | dbs.JSON | https |  |  | 监听的HTTPS配置 |
| AccessAddrs | dbs.JSON | accessAddrs |  |  | 外部访问地址 |
| Order | uint32 | order |  |  | 排序 |
| State | uint8 | state |  |  | 状态 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| Weight | uint32 | weight |  |  | 权重 |
| Status | dbs.JSON | status |  |  | 运行状态 |

## UserOrder

**文件:** `user_order_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | 用户订单 |
| UserId | uint64 | userId |  | User | 用户ID |
| Code | string | code |  |  | 订单号 |
| Type | string | type |  |  | 订单类型 |
| MethodId | uint32 | methodId |  | Method | 支付方式 |
| Status | string | status |  |  | 订单状态 |
| Amount | float64 | amount |  |  | 金额 |
| Params | dbs.JSON | params |  |  | 附加参数 |
| ExpiredAt | uint64 | expiredAt |  |  | 过期时间 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| CancelledAt | uint64 | cancelledAt |  |  | 取消时间 |
| FinishedAt | uint64 | finishedAt |  |  | 结束时间 |
| State | uint8 | state |  |  | 状态 |

## UserOrderLog

**文件:** `user_order_log_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AdminId | uint64 | adminId |  | Admin | 管理员ID |
| UserId | uint64 | userId |  | User | 用户ID |
| OrderId | uint64 | orderId |  | Order | 订单ID |
| Status | string | status |  |  | 状态 |
| Snapshot | dbs.JSON | snapshot |  |  | 状态快照 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |

## UserPlan

**文件:** `user_plan_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint32 | userId |  | User | 用户ID |
| PlanId | uint32 | planId |  | Plan | 套餐ID |
| IsOn | bool | isOn |  |  | 是否启用 |
| Name | string | name |  |  | 名称 |
| DayTo | string | dayTo |  |  | 结束日期 |
| State | uint8 | state |  |  | 状态 |

## UserPlanBandwidthStat

**文件:** `user_plan_bandwidth_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| UserPlanId | uint64 | userPlanId |  | UserPlan | 用户套餐ID |
| Day | string | day |  |  | 日期YYYYMMDD |
| TimeAt | string | timeAt |  |  | 时间点HHII |
| Bytes | uint64 | bytes |  |  | 带宽 |
| RegionId | uint32 | regionId |  | Region | 区域ID |
| TotalBytes | uint64 | totalBytes |  |  | 总流量 |
| AvgBytes | uint64 | avgBytes |  |  | 平均流量 |
| CachedBytes | uint64 | cachedBytes |  |  | 缓存的流量 |
| AttackBytes | uint64 | attackBytes |  |  | 攻击流量 |
| CountRequests | uint64 | countRequests |  |  | 请求数 |
| CountCachedRequests | uint64 | countCachedRequests |  |  | 缓存的请求数 |
| CountAttackRequests | uint64 | countAttackRequests |  |  | 攻击请求数 |
| CountWebsocketConnections | uint64 | countWebsocketConnections |  |  | Websocket连接数 |

## UserPlanStat

**文件:** `user_plan_stat_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserPlanId | uint64 | userPlanId |  | UserPlan | 用户套餐ID |
| Date | string | date |  |  | 日期：YYYYMMDD或YYYYMM |
| DateType | string | dateType |  |  | 日期类型：day|month |
| TrafficBytes | uint64 | trafficBytes |  |  | 流量 |
| CountRequests | uint64 | countRequests |  |  | 总请求数 |
| CountWebsocketConnections | uint64 | countWebsocketConnections |  |  | Websocket连接数 |
| IsProcessed | bool | isProcessed |  |  | 是否已处理 |

## UserScript

**文件:** `user_script_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| UserId | uint64 | userId |  | User | 用户ID |
| AdminId | uint64 | adminId |  | Admin | 操作管理员 |
| Code | string | code |  |  | 代码 |
| CodeMD5 | string | codeMD5 |  |  | 代码MD5 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| IsRejected | bool | isRejected |  |  | 是否已驳回 |
| RejectedAt | uint64 | rejectedAt |  |  | 驳回时间 |
| RejectedReason | string | rejectedReason |  |  | 驳回原因 |
| IsPassed | bool | isPassed |  |  | 是否通过审核 |
| PassedAt | uint64 | passedAt |  |  | 通过时间 |
| State | uint8 | state |  |  | 状态 |
| WebIds | dbs.JSON | webIds |  |  | WebId列表 |

## UserTrafficBill

**文件:** `user_traffic_bill_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| BillId | uint64 | billId |  | Bill | 主账单ID |
| RegionId | uint32 | regionId |  | Region | 区域ID |
| Amount | float64 | amount |  |  | 金额 |
| BandwidthMB | float64 | bandwidthMB |  |  | 带宽MB |
| BandwidthPercentile | uint8 | bandwidthPercentile |  |  | 带宽百分位 |
| TrafficGB | float64 | trafficGB |  |  | 流量GB |
| TrafficPackageGB | float64 | trafficPackageGB |  |  | 使用的流量包GB |
| UserTrafficPackageIds | dbs.JSON | userTrafficPackageIds |  |  | 使用的流量包ID |
| PricePerUnit | float64 | pricePerUnit |  |  | 单位价格 |
| PriceType | string | priceType |  |  | 计费方式：traffic|bandwidth |
| State | uint8 | state |  |  | 状态 |

## UserTrafficPackage

**文件:** `user_traffic_package_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| AdminId | uint32 | adminId |  | Admin | 管理员ID |
| UserId | uint64 | userId |  | User | 用户ID |
| PackageId | uint32 | packageId |  | Package | 流量包ID |
| TotalBytes | uint64 | totalBytes |  |  | 总字节数 |
| UsedBytes | uint64 | usedBytes |  |  | 已使用字节数 |
| RegionId | uint32 | regionId |  | Region | 区域ID |
| PeriodId | uint32 | periodId |  | Period | 有效期ID |
| PeriodCount | uint32 | periodCount |  |  | 有效期数量 |
| PeriodUnit | string | periodUnit |  |  | 有效期单位 |
| DayFrom | string | dayFrom |  |  | 开始日期 |
| DayTo | string | dayTo |  |  | 结束日期 |
| CreatedAt | uint64 | createdAt |  |  | 创建时间 |
| State | uint8 | state |  |  | 状态 |

## UserVerifyCode

**文件:** `user_verify_code_model.go`

| 字段名        | 类型     | 标签         | 主键  | 外键  | 注释       |
| ---------- | ------ | ---------- | --- | --- | -------- |
| Id         | uint64 | id         | ✓   |     | ID       |
| Email      | string | email      |     |     | 邮箱地址     |
| Mobile     | string | mobile     |     |     | 手机号      |
| Code       | string | code       |     |     | 验证码      |
| Type       | string | type       |     |     | 类型       |
| IsSent     | bool   | isSent     |     |     | 是否已发送    |
| IsVerified | bool   | isVerified |     |     | 是否已激活    |
| CreatedAt  | uint64 | createdAt  |     |     | 创建时间     |
| ExpiresAt  | uint64 | expiresAt  |     |     | 过期时间     |
| Day        | string | day        |     |     | YYYYMMDD |

## Version

**文件:** `version_model.go`

| 字段名 | 类型 | 标签 | 主键 | 外键 | 注释 |
|--------|------|------|------|------|------|
| Id | uint64 | id | ✓ |  | ID |
| Version | string | version |  |  |  |

# EdgeAPI 数据库实体关系图

  

本文档展示了 EdgeAPI 系统中主要数据表的关系结构。

  

```mermaid

erDiagram

    %% 核心用户管理表

    Admin {

        uint32 id PK

        bool isOn

        string username

        string password

        string fullname

        bool isSuper

        uint64 createdAt

        uint64 updatedAt

        uint8 state

        JSON modules

        bool canLogin

        string theme

        string lang

    }

  

    User {

        uint32 id PK

        bool isOn

        string username

        string password

        string fullname

        string email

        string mobile

        string verifiedEmail

        bool emailIsVerified

        uint32 avatarFileId FK

        uint64 createdAt

        string day

        uint64 updatedAt

        uint8 state

        string source

        uint32 clusterId FK

        JSON features

        string registeredIP

    }

  

    SubUser {

        uint32 id PK

        uint32 userId FK

        string username

        string fullname

        bool isOn

        uint64 createdAt

        uint8 state

    }

  

    %% 节点和集群管理

    NodeCluster {

        uint32 id PK

        bool isOn

        string name

        string note

        uint64 createdAt

        uint8 state

        uint32 adminId FK

        uint32 userId FK

        uint32 dnsProviderId FK

        string dnsDomainName

        JSON dnsResolver

        JSON cachePolicyId

        JSON httpFirewallPolicyId

        JSON systemServices

        JSON httpCC

        JSON httpPages

        JSON webp

        uint32 timeZone

    }

  

    Node {

        uint32 id PK

        bool isOn

        string uniqueId

        string secret

        string name

        string description

        uint32 order

        uint8 state

        uint64 createdAt

        uint32 adminId FK

        uint32 clusterId FK

        uint32 regionId FK

        uint32 groupId FK

        uint32 weight

        JSON status

        bool isActive

        JSON installStatus

        uint64 connectedAPINodes

        uint32 maxCacheDiskCapacity

        uint32 maxCacheMemoryCapacity

    }

  

    NodeGroup {

        uint32 id PK

        string name

        uint32 order

        bool isOn

        uint64 createdAt

        uint8 state

    }

  

    %% 服务器和域名管理

    Server {

        uint32 id PK

        uint32 adminId FK

        uint32 userId FK

        bool isOn

        uint8 type

        string name

        string description

        uint32 clusterId FK

        bool isAuditing

        JSON auditingResult

        JSON auditingServerNamesError

        string auditingAt

        JSON serverNames

        JSON http

        JSON https

        JSON tcp

        JSON tls

        JSON unix

        JSON udp

        uint32 webId FK

        uint32 reverseProxyId FK

        uint64 createdAt

        uint8 state

        JSON dnsName

        JSON supportCNAME

        JSON trafficLimit

        JSON userPlan

        JSON groupIds

        bool includeNodes

        bool excludeNodes

        JSON nodeClusterIds

    }

  

    ServerGroup {

        uint32 id PK

        uint32 adminId FK

        uint32 userId FK

        string name

        uint32 order

        bool isOn

        uint64 createdAt

        uint8 state

    }

  

    HTTPWeb {

        uint32 id PK

        bool isOn

        uint32 templateId FK

        uint32 adminId FK

        uint32 userId FK

        uint8 state

        uint64 createdAt

        JSON root

        JSON charset

        JSON shutdown

        JSON pages

        bool enableGlobalPages

        JSON redirectToHttps

        JSON indexes

        JSON maxRequestBodySize

        JSON requestHeader

        JSON responseHeader

        JSON accessLog

        JSON stat

        JSON compression

        JSON cache

        JSON firewall

        JSON locations

        JSON websocket

        JSON rewriteRules

        JSON hostRedirects

        JSON fastcgi

        JSON auth

        JSON webp

        JSON remoteAddr

        uint8 mergeSlashes

        JSON requestLimit

        JSON requestScripts

        JSON uam

        JSON cc

        JSON referers

        JSON userAgent

        JSON optimization

        JSON hls

    }

  

    %% 反向代理和源站

    ReverseProxy {

        uint32 id PK

        uint32 adminId FK

        uint32 userId FK

        uint32 templateId FK

        bool isOn

        JSON scheduling

        JSON primaryOrigins

        JSON backupOrigins

        string stripPrefix

        uint8 requestHostType

        string requestHost

        bool requestHostExcludingPort

        string requestURI

        uint8 autoFlush

        JSON addHeaders

        uint8 state

        uint64 createdAt

        JSON connTimeout

        JSON readTimeout

        JSON idleTimeout

        uint32 maxConns

        uint32 maxIdleConns

        JSON proxyProtocol

        uint8 followRedirects

        uint8 retry50X

        uint8 retry40X

    }

  

    Origin {

        uint32 id PK

        uint32 adminId FK

        uint32 userId FK

        uint64 reverseProxyId FK

        bool isOn

        string name

        uint32 version

        JSON addr

        JSON oss

        string description

        string code

        uint32 weight

        JSON connTimeout

        JSON readTimeout

        JSON idleTimeout

        uint32 maxFails

        uint32 maxConns

        uint32 maxIdleConns

        string httpRequestURI

        JSON httpRequestHeader

        JSON httpResponseHeader

        JSON host

        uint8 state

        uint64 createdAt

        uint32 ftp

        JSON healthCheck

        JSON cert

        bool followRedirects

        JSON http2Enabled

        uint32 domains

    }

  

    %% 文件和缓存管理

    File {

        uint32 id PK

        uint32 adminId FK

        string code

        uint32 userId FK

        string description

        string filename

        uint32 size

        string mimeType

        uint64 createdAt

        uint32 order

        string type

        uint8 state

        bool isFinished

        bool isPublic

    }

  

    FileChunk {

        uint32 id PK

        uint32 fileId FK

        bytes data

    }

  

    %% HTTP 相关策略

    HTTPFirewallPolicy {

        uint32 id PK

        uint32 templateId FK

        uint32 adminId FK

        uint32 userId FK

        uint32 serverId FK

        uint32 groupId FK

        uint8 state

        uint64 createdAt

        bool isOn

        string name

        string description

        JSON inbound

        JSON outbound

        JSON blockOptions

        JSON pageOptions

        JSON captchaOptions

        JSON jsCookieOptions

        string mode

        uint8 useLocalFirewall

        JSON synFlood

        JSON log

        uint32 maxRequestBodySize

        string denyCountryHTML

        string denyProvinceHTML

    }

  

    HTTPCachePolicy {

        uint32 id PK

        uint32 adminId FK

        uint32 userId FK

        uint32 templateId FK

        bool isOn

        string name

        JSON capacity

        uint64 maxKeys

        JSON maxSize

        string type

        JSON options

        uint64 createdAt

        uint8 state

        string description

        JSON refs

        uint8 syncCompressionCache

        JSON fetchTimeout

    }

  

    %% 访问日志

    HTTPAccessLog {

        uint64 id PK

        uint32 serverId FK

        uint32 nodeId FK

        uint32 status

        uint64 createdAt

        JSON content

        string requestId

        uint32 firewallPolicyId FK

        uint32 firewallRuleGroupId FK

        uint32 firewallRuleSetId FK

        uint32 firewallRuleId FK

        string remoteAddr

        string domain

        bytes requestBody

        bytes responseBody

    }

  

    %% 访问控制

    UserAccessKey {

        uint32 id PK

        uint32 adminId FK

        uint32 userId FK

        uint32 subUserId FK

        bool isOn

        string uniqueId

        string secret

        string description

        uint64 accessedAt

        uint8 state

    }

  

    LoginSession {

        uint64 id PK

        uint64 adminId FK

        uint64 userId FK

        string sid

        JSON values

        string ip

        uint64 createdAt

        uint64 expiresAt

    }

  

    %% 消息和日志系统

    Message {

        uint64 id PK

        uint32 adminId FK

        uint32 userId FK

        string role

        uint32 clusterId FK

        uint32 nodeId FK

        string level

        string subject

        string body

        string type

        JSON params

        bool isRead

        uint8 state

        uint64 createdAt

        string day

        string hash

    }

  

    Log {

        uint32 id PK

        string level

        string description

        uint64 createdAt

        string action

        uint32 userId FK

        uint32 adminId FK

        uint32 providerId FK

        string ip

        string type

        string day

        uint32 billId FK

        string langMessageCode

        JSON langMessageArgs

        JSON params

    }

  

    %% IP 管理

    IPList {

        uint32 id PK

        bool isOn

        string type

        uint32 adminId FK

        uint32 userId FK

        uint64 serverId FK

        string name

        string code

        uint8 state

        uint64 createdAt

        JSON timeout

        JSON actions

        string description

        bool isPublic

        bool isGlobal

    }

  

    IPItem {

        uint64 id PK

        uint32 listId FK

        string value

        string type

        string ipFrom

        string ipTo

        uint64 ipFromLong

        uint64 ipToLong

        uint64 version

        uint64 createdAt

        uint64 updatedAt

        string reason

        string eventLevel

        uint8 state

        uint64 expiredAt

        uint32 serverId FK

        uint32 nodeId FK

        uint32 sourceNodeId FK

        uint32 sourceServerId FK

        uint32 sourceHTTPFirewallPolicyId FK

        uint32 sourceHTTPFirewallRuleGroupId FK

        uint32 sourceHTTPFirewallRuleSetId FK

        uint64 sourceUserId FK

        bool isRead

    }

  

    %% 关系定义

    User ||--o{ SubUser : "hasSubUsers"

    User ||--o{ Server : "ownsServers"

    User ||--o{ UserAccessKey : "hasAccessKeys"

    User ||--o{ File : "ownsFiles"

    User ||--o{ HTTPWeb : "configuresWeb"

    User ||--o{ ReverseProxy : "configuresProxy"

    User ||--o{ Origin : "configuresOrigins"

    User ||--o{ HTTPFirewallPolicy : "configuresFirewall"

    User ||--o{ HTTPCachePolicy : "configuresCache"

    User ||--o{ IPList : "managesIPLists"

    User ||--o{ Message : "receivesMessages"

    User ||--o{ Log : "generatesLogs"

    User ||--o{ LoginSession : "hasSessions"

  

    Admin ||--o{ Server : "managesServers"

    Admin ||--o{ Node : "managesNodes"

    Admin ||--o{ NodeCluster : "managesClusters"

    Admin ||--o{ User : "managesUsers"

    Admin ||--o{ UserAccessKey : "managesAccessKeys"

    Admin ||--o{ File : "managesFiles"

    Admin ||--o{ HTTPWeb : "managesWeb"

    Admin ||--o{ ReverseProxy : "managesProxy"

    Admin ||--o{ Origin : "managesOrigins"

    Admin ||--o{ HTTPFirewallPolicy : "managesFirewall"

    Admin ||--o{ HTTPCachePolicy : "managesCache"

    Admin ||--o{ IPList : "managesIPLists"

    Admin ||--o{ Message : "sendsMessages"

    Admin ||--o{ Log : "generatesLogs"

    Admin ||--o{ LoginSession : "hasSessions"

  

    NodeCluster ||--o{ Node : "containsNodes"

    NodeCluster ||--o{ Server : "servesServers"

    NodeCluster ||--o{ Message : "receivesMessages"

  

    Node ||--o{ HTTPAccessLog : "generatesLogs"

    Node ||--o{ IPItem : "managesIPs"

    Node ||--o{ Message : "receivesMessages"

  

    NodeGroup ||--o{ Node : "groupsNodes"

  

    Server ||--o{ HTTPAccessLog : "generatesAccessLogs"

    Server ||--o{ IPItem : "managesIPItems"

    Server ||--o{ IPList : "usesIPLists"

    Server ||--o{ HTTPFirewallPolicy : "usesFirewallPolicy"

  

    ServerGroup ||--o{ Server : "groupsServers"

  

    HTTPWeb ||--o{ Server : "configuresServer"

  

    ReverseProxy ||--o{ Server : "servesServer"

    ReverseProxy ||--o{ Origin : "usesOrigins"

  

    File ||--o{ FileChunk : "hasChunks"

    File ||--o{ User : "belongsToUser"

  

    IPList ||--o{ IPItem : "containsItems"

  

    HTTPFirewallPolicy ||--o{ HTTPAccessLog : "logsAccess"

  

    UserAccessKey ||--o{ SubUser : "belongsToSubUser"

```

  

## 主要表关系说明

  

### 1. 用户管理体系

- **Admin**: 系统管理员，可以管理所有资源

- **User**: 普通用户，拥有自己的服务器和配置

- **SubUser**: 子用户，隶属于主用户

- **UserAccessKey**: API访问密钥，用于身份验证

- **LoginSession**: 登录会话管理

  

### 2. 节点与集群体系

- **NodeCluster**: 节点集群，是节点的逻辑分组

- **Node**: 边缘节点，实际提供服务的服务器

- **NodeGroup**: 节点分组，用于管理相似节点

  

### 3. 服务器与域名体系

- **Server**: 服务器配置，定义了域名、端口、协议等

- **ServerGroup**: 服务器分组

- **HTTPWeb**: HTTP网站配置

- **ReverseProxy**: 反向代理配置

- **Origin**: 源站配置

  

### 4. 安全与防护体系

- **HTTPFirewallPolicy**: HTTP防火墙策略

- **IPList**: IP名单（黑白名单）

- **IPItem**: IP名单中的具体项目

  

### 5. 缓存与存储体系

- **HTTPCachePolicy**: HTTP缓存策略

- **File**: 文件管理

- **FileChunk**: 文件分块存储

  

### 6. 日志与监控体系

- **HTTPAccessLog**: HTTP访问日志

- **Log**: 系统操作日志

- **Message**: 系统消息

  

### 7. 核心外键关系

- 所有资源都通过 `adminId` 或 `userId` 关联到管理员或用户

- 服务器通过 `clusterId` 关联到节点集群

- 节点通过 `clusterId` 关联到节点集群

- 访问日志通过 `serverId` 和 `nodeId` 关联到具体的服务器和节点

- IP项目通过 `listId` 关联到IP名单

- 文件分块通过 `fileId` 关联到文件

  

这个ERD图展示了 EdgeAPI 系统的核心数据架构，说明了各个模块之间的关联关系和数据流向。
